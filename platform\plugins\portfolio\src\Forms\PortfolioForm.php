<?php

namespace Shaqi\Portfolio\Forms;

use Shaqi\Base\Forms\FieldOptions\ContentFieldOption;
use Shaqi\Base\Forms\FieldOptions\DescriptionFieldOption;
use Shaqi\Base\Forms\FieldOptions\IsFeaturedFieldOption;
use Shaqi\Base\Forms\FieldOptions\MediaImageFieldOption;
use Shaqi\Base\Forms\FieldOptions\NameFieldOption;
use Shaqi\Base\Forms\FieldOptions\SelectFieldOption;
use Shaqi\Base\Forms\FieldOptions\StatusFieldOption;
use Shaqi\Base\Forms\FieldOptions\TagFieldOption;
use Shaqi\Base\Forms\Fields\EditorField;
use Shaqi\Base\Forms\Fields\MediaImageField;
use Shaqi\Base\Forms\Fields\OnOffField;
use Shaqi\Base\Forms\Fields\SelectField;
use Shaqi\Base\Forms\Fields\TagField;
use Shaqi\Base\Forms\Fields\TextareaField;
use Shaqi\Base\Forms\Fields\TextField;
use Shaqi\Base\Forms\Fields\TreeCategoryField;
use Shaqi\Base\Forms\Fields\RepeaterField;
use Shaqi\Base\Forms\Fields\MediaImagesField;
use Shaqi\Base\Forms\FormAbstract;
use Shaqi\Portfolio\Http\Requests\PortfolioRequest;
use Shaqi\Portfolio\Models\Portfolio;
use Shaqi\Portfolio\Models\PortfolioCategory;
use Shaqi\Portfolio\Models\PortfolioTag;
use Shaqi\Portfolio\Enums\PortfolioStatusEnum;

class PortfolioForm extends FormAbstract
{
    public function setup(): void
    {
        $this
            ->model(Portfolio::class)
            ->setValidatorClass(PortfolioRequest::class)
            ->add('name', TextField::class, NameFieldOption::make()->required())
            ->add('description', TextareaField::class, DescriptionFieldOption::make())
            ->add(
                'is_featured',
                OnOffField::class,
                IsFeaturedFieldOption::make()
            )
            ->add(
                'content',
                EditorField::class,
                ContentFieldOption::make()
                    ->allowedShortcodes()
                    ->placeholder(trans('core/base::forms.write_content'))
            )
            ->add(
                'features',
                RepeaterField::class,
                [
                    'label' => trans('plugins/portfolio::portfolios.form.features'),
                    'fields' => [
                        [
                            'type' => 'text',
                            'name' => 'feature',
                            'label' => trans('plugins/portfolio::portfolios.form.feature'),
                            'attributes' => [
                                'name' => 'features[]',
                                'value' => null,
                                'options' => [
                                    'class' => 'form-control',
                                    'placeholder' => trans('plugins/portfolio::portfolios.form.feature_placeholder'),
                                ],
                            ],
                        ],
                    ],
                    'value' => $this->getModel()->features ?: [],
                ]
            )
            ->add(
                'gallery',
                MediaImagesField::class,
                [
                    'label' => trans('plugins/portfolio::portfolios.form.gallery'),
                    'values' => $this->getModel()->gallery ?: [],
                ]
            )
            ->add(
                'status',
                SelectField::class,
                StatusFieldOption::make()
                    ->choices(PortfolioStatusEnum::labels())
                    ->selected(PortfolioStatusEnum::PUBLISHED)
            )
            ->add(
                'categories',
                TreeCategoryField::class,
                SelectFieldOption::make()
                    ->label(trans('plugins/portfolio::portfolios.form.categories'))
                    ->choices(PortfolioCategory::query()->wherePublished()->pluck('name', 'id')->all())
                    ->selected(old('categories', $this->getModel()->categories()->pluck('id')->all()))
                    ->addAttribute('class', 'list-item-checkbox')
                    ->multiple()
            )
            ->add('image', MediaImageField::class, MediaImageFieldOption::make())
            ->add(
                'logo',
                MediaImageField::class,
                MediaImageFieldOption::make()
                    ->label(trans('plugins/portfolio::portfolios.form.logo'))
            )
            ->add(
                'website_link',
                TextField::class,
                [
                    'label' => trans('plugins/portfolio::portfolios.form.website_link'),
                    'placeholder' => 'https://example.com',
                    'attr' => ['class' => 'form-control'],
                ]
            )


            ->add(
                'tag',
                TagField::class,
                TagFieldOption::make()
                    ->label(trans('plugins/portfolio::portfolios.form.tags'))
                    ->when($this->getModel()->getKey(), function (TagFieldOption $fieldOption) {
                        /**
                         * @var Portfolio $portfolio
                         */
                        $portfolio = $this->getModel();

                        return $fieldOption
                            ->selected(
                                $portfolio
                                    ->tags()
                                    ->select('name')
                                    ->get()
                                    ->map(fn (PortfolioTag $item) => $item->name)
                                    ->implode(',')
                            );
                    })
                    ->placeholder(trans('plugins/portfolio::base.write_some_tags'))
                    ->ajaxUrl(route('portfolio-tags.all'))
            )
            ->setBreakFieldPoint('status');
    }
}
