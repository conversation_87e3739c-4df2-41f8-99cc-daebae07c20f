<?php

namespace <PERSON>haqi\Portfolio\Http\Requests;

use <PERSON>haqi\Base\Enums\BaseStatusEnum;
use <PERSON>haqi\Base\Rules\MediaImageRule;
use <PERSON>haqi\Base\Rules\OnOffRule;
use <PERSON>haqi\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class PortfolioRequest extends Request
{
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:400',
            'content' => 'nullable|string',
            'image' => ['nullable', 'string', new MediaImageRule()],
            'logo' => ['nullable', 'string', new MediaImageRule()],
            'website_link' => 'nullable|url|max:255',
            'features' => 'nullable|array',
            'features.*' => 'nullable|string|max:255',
            'gallery' => 'nullable|array',
            'gallery.*' => ['nullable', 'string', new MediaImageRule()],
            'is_featured' => [new OnOffRule()],
            'status' => Rule::in(BaseStatusEnum::values()),
            'categories' => 'nullable|array',
            'categories.*' => 'nullable|integer|exists:portfolio_categories,id',
            'tag' => 'nullable|string',
        ];
    }
}
