{"__meta": {"id": "X92661c52f1216efba7f33d7dd0dff0f2", "datetime": "2025-07-15 23:18:31", "utime": **********.69486, "method": "POST", "uri": "/ajax/slug/create", "ip": "127.0.0.1"}, "php": {"version": "8.3.17", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752621510.523289, "end": **********.694917, "duration": 1.1716279983520508, "duration_str": "1.17s", "measures": [{"label": "Booting", "start": 1752621510.523289, "relative_start": 0, "end": **********.582113, "relative_end": **********.582113, "duration": 1.058824062347412, "duration_str": "1.06s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.582236, "relative_start": 1.0589470863342285, "end": **********.694922, "relative_end": 5.0067901611328125e-06, "duration": 0.1126859188079834, "duration_str": "113ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 41562640, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST ajax/slug/create", "middleware": "web, core", "as": "slug.create", "controller": "Sha<PERSON>\\Slug\\Http\\Controllers\\SlugController@store", "namespace": "<PERSON><PERSON><PERSON>\\Slug\\Http\\Controllers", "prefix": "/ajax/slug", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fslug%2Fsrc%2FHttp%2FControllers%2FSlugController.php&line=18\" onclick=\"\">vendor/shaqi/slug/src/Http/Controllers/SlugController.php:18-25</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00318, "accumulated_duration_str": "3.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select exists(select * from `slugs` where (`key` = 'northgate-animal-hospital' and `prefix` = 'portfolio') and `id` != '0') as `exists`", "type": "query", "params": [], "bindings": ["northgate-animal-hospital", "portfolio", "0"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/shaqi/slug/src/Services/SlugService.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\slug\\src\\Services\\SlugService.php", "line": 42}, {"index": 12, "namespace": null, "name": "vendor/shaqi/slug/src/Services/SlugService.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\slug\\src\\Services\\SlugService.php", "line": 23}, {"index": 13, "namespace": null, "name": "vendor/shaqi/slug/src/Http/Controllers/SlugController.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\slug\\src\\Http\\Controllers\\SlugController.php", "line": 20}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.682028, "duration": 0.00318, "duration_str": "3.18ms", "memory": 0, "memory_str": null, "filename": "SlugService.php:42", "source": {"index": 11, "namespace": null, "name": "vendor/shaqi/slug/src/Services/SlugService.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\slug\\src\\Services\\SlugService.php", "line": 42}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fslug%2Fsrc%2FServices%2FSlugService.php&line=42", "ajax": false, "filename": "SlugService.php", "line": "42"}, "connection": "goalconversion", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "jbzeKOEqSud6pSChnp17wAvUgUTGQrZr4mytzMah", "_previous": "array:1 [\n  \"url\" => \"https://goalconversion.gc/gc/lgn/portfolio/portfolios/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"path_info": "/ajax/slug/create", "status_code": "<pre class=sf-dump id=sf-dump-984165892 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-984165892\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-935914248 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-935914248\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-401777930 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Northgate Animal Hospital</span>\"\n  \"<span class=sf-dump-key>slug_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>model</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Shaqi\\Portfolio\\Models\\Portfolio</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jbzeKOEqSud6pSChnp17wAvUgUTGQrZr4mytzMah</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-401777930\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2001284054 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">goalconversion.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">149</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjljTkszNitPSy9zWHE1dmt5U3ZJdFE9PSIsInZhbHVlIjoiT3h5OERWQlJyN054SjU3WHk5NHEyS1M4dFFMRG1NSFE0Uzhta0dEWnRGOVJ4VVdNcW1mU1hGb01mVTdMdk5tRG4veFhGZjB5RmVWaUdOb05ydklqNzJkV1lPR3ZEN3F3eUdqYUdTaFhoaGJHeVlSMlRxY05EZ2JFY2t3VzczTDEiLCJtYWMiOiI4ZmQ3MDA5ZjEwOTc2ODlhMGMzY2ZmNDU3NDQxODhhMjEzYjJkNTZlYTMyMjc0MTliZTUwNmNmZTdjMTMxM2VlIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">https://goalconversion.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"60 characters\">https://goalconversion.gc/gc/lgn/portfolio/portfolios/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1370 characters\">_ga=GA1.1.996387623.1750873576; cookie_for_consent=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjlFUmdOdDJTZlc2S0NuaFZHV01wWFE9PSIsInZhbHVlIjoiM3JYNzlHNUxRbDYvT2xLc0lxeTI0alRqcThNaVRrRXk4N1RGdEZGLy9QVnM4ZmFUZzdhckN1bCtIeGc0UDgzRTdNQkNtN0dlSHU2YmVURHIrMDA5YnlzeG82Yzh6WnhjcTlENEVwazRCRzgvdE9naDVjWG5kNzFTYkgySWNsSlk1OGhyS1pNai9ReFdleW5QZFdndHBQbHZYck13cGc0ZjZvUHNHTk1KOVcrVFExRURSL2lkaHp4cXMwUlc0RTViRnN5akMzU25RTDQzeXNoWG9XU2hZUVE5aG5xVUh2bWJiWWV3a3pmdGFPdz0iLCJtYWMiOiIxYTk2ZTQ1ODRjMTQ3OTc0OWVlZmQyY2JkN2IyMzdlODEzYWZmMTkyZWJjMThlNWRhZmM2NjlmMTdiODIzYmFmIiwidGFnIjoiIn0%3D; _ga_FSKD72EXSB=GS2.1.s1752619353$o34$g1$t1752620286$j60$l0$h0; XSRF-TOKEN=eyJpdiI6IjljTkszNitPSy9zWHE1dmt5U3ZJdFE9PSIsInZhbHVlIjoiT3h5OERWQlJyN054SjU3WHk5NHEyS1M4dFFMRG1NSFE0Uzhta0dEWnRGOVJ4VVdNcW1mU1hGb01mVTdMdk5tRG4veFhGZjB5RmVWaUdOb05ydklqNzJkV1lPR3ZEN3F3eUdqYUdTaFhoaGJHeVlSMlRxY05EZ2JFY2t3VzczTDEiLCJtYWMiOiI4ZmQ3MDA5ZjEwOTc2ODlhMGMzY2ZmNDU3NDQxODhhMjEzYjJkNTZlYTMyMjc0MTliZTUwNmNmZTdjMTMxM2VlIiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6ImZEa3cvR0VkQmFMeUZGbmF0amsxUEE9PSIsInZhbHVlIjoiZjBTWEdmMWlmaWcvcDNjR0FUQXl4c3VRWEhWcHlPSFZDSXdIcEY5cWh2NXFsQUE0bXYwdkE1Um5USG5kOWxuMFFURlcvUGJ2NWlKUS93SEdIRnlTQ09KR1NLVTIrdExVdGZuQ0NGU284eXlzWStuNTFyNnhpcE8rclIxQ2FzYTciLCJtYWMiOiIyNmI2YzJjOWU5NGE5YmY2MmM3MmEwOWZhYjJmZjc3Mzg4YzY1MzIxMTdkODY2YWFmMjYzOWI3NDEzN2UzMWY0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2001284054\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|o3CnvmCBSSL98ViBJdgJN5so8aDe5dXi7VGRA2SAZJYu2BSuxQ6yHeXfJ63N|$2y$12$4xmFIOy4V2zK3g4n9iqTEuh93mpXc3HCy/DDmxWzDBRr3EJiAeNFG</span>\"\n  \"<span class=sf-dump-key>_ga_FSKD72EXSB</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jbzeKOEqSud6pSChnp17wAvUgUTGQrZr4mytzMah</span>\"\n  \"<span class=sf-dump-key>shaqi_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p1qIhFLYEVd1Cf5BIAVCAxzvLAKTjzJ7cjgyhoKx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1273262683 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 15 Jul 2025 23:18:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6IjVPRnMyY0JGTnVQTEhqWHk2RXpDelE9PSIsInZhbHVlIjoiTmlwTWllZHdXZDBaYStaVkkyZE5NYVZHZDh3amZBMDNjSGd4ZUdvODl2Vm4xWG1DRzE4WDN2WlF4N3NDTkEzWExFZG5WTmZYZkRGTlpNNXRBTEVRbU5ObmhUd0ZvMmp5ZldWQTVNOVNiVS9ydjhYc3pJRFpkdkdnb2JDYldIbGgiLCJtYWMiOiIyYmY5ZWMwYWMzM2Q2ZGY5Y2NhZTg2OGFkOWIzNjc0MWRmZjhlMDg3OWMwMjMyZDBiMWRmODg0ZGRlZWY1OWMzIiwidGFnIjoiIn0%3D; expires=Wed, 16 Jul 2025 01:18:31 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">shaqi_session=eyJpdiI6IjBaUFFZemxkbldkY2NOWnhIZEI5cEE9PSIsInZhbHVlIjoicXRJQnBGY3JrenNocnhPSDRFaHhZRmd1dVRWRjNCWk5ucjVnNGUzTDBsWTNSTy9pMHZuQ3ByUUgrZ2RIUFBSNEhkR2dhcjFLSEJyVW1maHJjSXJDeXk5SWtNdzA5cUpsTUQ4L0VqamFTQjFyOGtDQTRZRU92c1I2RDBiby9YY0kiLCJtYWMiOiJlYWVjM2M1OWYzYjRkZjUzY2IwN2MyNjMxMzliMWFhNmE2OWI2M2Y5YWFjZjU4OTU5MjdmYWYxMjg4ZTNlMDQzIiwidGFnIjoiIn0%3D; expires=Wed, 16 Jul 2025 01:18:31 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6IjVPRnMyY0JGTnVQTEhqWHk2RXpDelE9PSIsInZhbHVlIjoiTmlwTWllZHdXZDBaYStaVkkyZE5NYVZHZDh3amZBMDNjSGd4ZUdvODl2Vm4xWG1DRzE4WDN2WlF4N3NDTkEzWExFZG5WTmZYZkRGTlpNNXRBTEVRbU5ObmhUd0ZvMmp5ZldWQTVNOVNiVS9ydjhYc3pJRFpkdkdnb2JDYldIbGgiLCJtYWMiOiIyYmY5ZWMwYWMzM2Q2ZGY5Y2NhZTg2OGFkOWIzNjc0MWRmZjhlMDg3OWMwMjMyZDBiMWRmODg0ZGRlZWY1OWMzIiwidGFnIjoiIn0%3D; expires=Wed, 16-Jul-2025 01:18:31 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">shaqi_session=eyJpdiI6IjBaUFFZemxkbldkY2NOWnhIZEI5cEE9PSIsInZhbHVlIjoicXRJQnBGY3JrenNocnhPSDRFaHhZRmd1dVRWRjNCWk5ucjVnNGUzTDBsWTNSTy9pMHZuQ3ByUUgrZ2RIUFBSNEhkR2dhcjFLSEJyVW1maHJjSXJDeXk5SWtNdzA5cUpsTUQ4L0VqamFTQjFyOGtDQTRZRU92c1I2RDBiby9YY0kiLCJtYWMiOiJlYWVjM2M1OWYzYjRkZjUzY2IwN2MyNjMxMzliMWFhNmE2OWI2M2Y5YWFjZjU4OTU5MjdmYWYxMjg4ZTNlMDQzIiwidGFnIjoiIn0%3D; expires=Wed, 16-Jul-2025 01:18:31 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1273262683\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-564886822 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jbzeKOEqSud6pSChnp17wAvUgUTGQrZr4mytzMah</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"60 characters\">https://goalconversion.gc/gc/lgn/portfolio/portfolios/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-564886822\", {\"maxDepth\":0})</script>\n"}}