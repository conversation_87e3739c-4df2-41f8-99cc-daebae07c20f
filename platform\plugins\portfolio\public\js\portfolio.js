'use strict'

$(document).ready(function () {
    // Portfolio gallery management
    if ($('.list-gallery-media-images').length > 0) {
        initPortfolioGallery()
    }

    // Features repeater field management
    if ($('.repeater-field[data-name="features"]').length > 0) {
        initFeaturesRepeater()
    }
})

function initPortfolioGallery() {
    // Handle gallery image selection
    $(document).on('click', '[data-bb-toggle="gallery-add"]', function (e) {
        e.preventDefault()
        
        const $this = $(this)
        const name = $this.data('name')
        
        window.rvMedia({
            multiple: true,
            filter: 'image',
            onSelectFiles: function (files) {
                if (files && files.length > 0) {
                    addGalleryImages(files, name)
                    $this.addClass('hidden')
                    $(`.list-gallery-media-images[data-name="${name}"]`).removeClass('hidden')
                }
            }
        })
    })

    // Handle gallery image removal
    $(document).on('click', '.btn-remove-gallery-image', function (e) {
        e.preventDefault()
        
        const $item = $(this).closest('.gallery-image-item-handler')
        const $container = $item.closest('.list-gallery-media-images')
        const name = $container.data('name')
        
        $item.remove()
        updateGalleryInput(name)
        
        // Show add button if no images left
        if ($container.find('.gallery-image-item-handler').length === 0) {
            $container.addClass('hidden')
            $(`[data-bb-toggle="gallery-add"][data-name="${name}"]`).removeClass('hidden')
        }
    })

    // Handle gallery image reordering
    $('.list-gallery-media-images').each(function () {
        const $container = $(this)
        const name = $container.data('name')
        
        if (typeof $.fn.sortable !== 'undefined') {
            $container.sortable({
                items: '.gallery-image-item-handler',
                cursor: 'move',
                placeholder: 'gallery-image-placeholder',
                update: function () {
                    updateGalleryInput(name)
                }
            })
        }
    })
}

function addGalleryImages(files, name) {
    const $container = $(`.list-gallery-media-images[data-name="${name}"]`)
    
    files.forEach(function (file) {
        const imageHtml = `
            <div class="col-lg-2 col-md-3 col-4 gallery-image-item-handler mb-2">
                <div class="custom-image-box image-box">
                    <input class="image-data" name="${name}" type="hidden" value="${file.url}">
                    <div class="preview-image-wrapper w-100 mb-1">
                        <div class="preview-image-inner">
                            <img class="preview-image" src="${file.thumb || file.url}" alt="Gallery Image">
                            <div class="image-picker-backdrop"></div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-sm btn-danger btn-remove-gallery-image">
                        <i class="fa fa-trash"></i>
                    </button>
                </div>
            </div>
        `
        $container.append(imageHtml)
    })
    
    updateGalleryInput(name)
}

function updateGalleryInput(name) {
    const $container = $(`.list-gallery-media-images[data-name="${name}"]`)
    const images = []
    
    $container.find('.image-data').each(function () {
        const value = $(this).val()
        if (value) {
            images.push(value)
        }
    })
    
    // Update the main hidden input
    $(`input[name="${name}"]`).val(JSON.stringify(images))
}

function initFeaturesRepeater() {
    // Handle adding new feature
    $(document).on('click', '.btn-add-repeater-item', function (e) {
        e.preventDefault()
        
        const $container = $(this).closest('.repeater-field').find('.repeater-items')
        const template = $container.data('template')
        const index = $container.find('.repeater-item').length
        
        if (template) {
            const newItem = template.replace(/\[INDEX\]/g, index)
            $container.append(newItem)
        }
    })

    // Handle removing feature
    $(document).on('click', '.btn-remove-repeater-item', function (e) {
        e.preventDefault()
        $(this).closest('.repeater-item').remove()
    })
}

// Initialize gallery on page load if data exists
function initExistingGallery() {
    $('.list-gallery-media-images').each(function () {
        const $container = $(this)
        const name = $container.data('name')
        
        if ($container.find('.gallery-image-item-handler').length > 0) {
            $container.removeClass('hidden')
            $(`[data-bb-toggle="gallery-add"][data-name="${name}"]`).addClass('hidden')
        }
    })
}

// Call on document ready
$(document).ready(function () {
    initExistingGallery()
})
