{"__meta": {"id": "Xb3511efd613be83b607256320471a787", "datetime": "2025-07-15 23:05:45", "utime": 1752620745.719759, "method": "POST", "uri": "/gc/lgn/membership/authorize", "ip": "127.0.0.1"}, "php": {"version": "8.3.17", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752620734.4094, "end": 1752620745.719792, "duration": 11.310391902923584, "duration_str": "11.31s", "measures": [{"label": "Booting", "start": 1752620734.4094, "relative_start": 0, "end": **********.559633, "relative_end": **********.559633, "duration": 1.1502330303192139, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.559653, "relative_start": 1.1502530574798584, "end": 1752620745.719796, "relative_end": 4.0531158447265625e-06, "duration": 10.16014289855957, "duration_str": "10.16s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 42359904, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST gc/lgn/membership/authorize", "middleware": "web, core, auth", "permission": false, "as": "membership.authorize", "controller": "Shaqi\\Base\\Http\\Controllers\\SystemController@postAuthorize", "namespace": "Shaqi\\Base\\Http\\Controllers", "prefix": "/gc/lgn", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FHttp%2FControllers%2FSystemController.php&line=31\" onclick=\"\">vendor/shaqi/platform/base/src/Http/Controllers/SystemController.php:31-36</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.018920000000000003, "accumulated_duration_str": "18.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.623878, "duration": 0.018920000000000003, "duration_str": "18.92ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"Shaqi\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "jbzeKOEqSud6pSChnp17wAvUgUTGQrZr4mytzMah", "_previous": "array:1 [\n  \"url\" => \"https://goalconversion.gc/gc/lgn/portfolio/portfolios/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"path_info": "/gc/lgn/membership/authorize", "status_code": "<pre class=sf-dump id=sf-dump-179713532 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-179713532\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1553604905 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1553604905\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-119098593 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-119098593\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-611868230 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">goalconversion.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ijg2aFpwSnRoOVg4THhMQTZabmx1eXc9PSIsInZhbHVlIjoiKzNNQ1JhaTFDM2lXc2c2L3dxVjJLdEFFYXRjakdZMDg1bWZFMElsbURYeGVXMXUwQ2NZdDZLYTNaVmpkenFGUms2eTE1V04wamczN2JGYWlheUZhaE0wMXhHa3NZRGNzRzFPd2hjbDVqOEFsVy9UbU1pbHdxREltOHF3dzhwTmciLCJtYWMiOiI3M2QyMWExZTM4ZGQ1ZThlN2IzM2YzNTQ4M2ViNjc5ZTAyZjFlMDM0OTUxMTJhNDg0OTY1MDJmNGZmYWE5MDM5IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">https://goalconversion.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"60 characters\">https://goalconversion.gc/gc/lgn/portfolio/portfolios/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1370 characters\">_ga=GA1.1.996387623.1750873576; cookie_for_consent=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjlFUmdOdDJTZlc2S0NuaFZHV01wWFE9PSIsInZhbHVlIjoiM3JYNzlHNUxRbDYvT2xLc0lxeTI0alRqcThNaVRrRXk4N1RGdEZGLy9QVnM4ZmFUZzdhckN1bCtIeGc0UDgzRTdNQkNtN0dlSHU2YmVURHIrMDA5YnlzeG82Yzh6WnhjcTlENEVwazRCRzgvdE9naDVjWG5kNzFTYkgySWNsSlk1OGhyS1pNai9ReFdleW5QZFdndHBQbHZYck13cGc0ZjZvUHNHTk1KOVcrVFExRURSL2lkaHp4cXMwUlc0RTViRnN5akMzU25RTDQzeXNoWG9XU2hZUVE5aG5xVUh2bWJiWWV3a3pmdGFPdz0iLCJtYWMiOiIxYTk2ZTQ1ODRjMTQ3OTc0OWVlZmQyY2JkN2IyMzdlODEzYWZmMTkyZWJjMThlNWRhZmM2NjlmMTdiODIzYmFmIiwidGFnIjoiIn0%3D; _ga_FSKD72EXSB=GS2.1.s1752619353$o34$g1$t1752620286$j60$l0$h0; XSRF-TOKEN=eyJpdiI6Ijg2aFpwSnRoOVg4THhMQTZabmx1eXc9PSIsInZhbHVlIjoiKzNNQ1JhaTFDM2lXc2c2L3dxVjJLdEFFYXRjakdZMDg1bWZFMElsbURYeGVXMXUwQ2NZdDZLYTNaVmpkenFGUms2eTE1V04wamczN2JGYWlheUZhaE0wMXhHa3NZRGNzRzFPd2hjbDVqOEFsVy9UbU1pbHdxREltOHF3dzhwTmciLCJtYWMiOiI3M2QyMWExZTM4ZGQ1ZThlN2IzM2YzNTQ4M2ViNjc5ZTAyZjFlMDM0OTUxMTJhNDg0OTY1MDJmNGZmYWE5MDM5IiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6IkxMT2NJUlUvamF6Y3JPQUxzNlZKZGc9PSIsInZhbHVlIjoiSGE4dWFROGhaZno5eDRrdlZDY3FkNGJaWkwyZ05FenJ2anpDUXRCUzRocjU0M2FpMm5Lb25kd0FFVE5GY255Y0hSYnlxRXV0Z2VTRk9uRkxDaldBWnRIamQvT0ZpRUNkVHNKMEIwMDcxdUlyMWdkMktFSkk4c2plMitsMHIxNlUiLCJtYWMiOiI4M2M1OTRiZmQ4MGZjNmE1Y2E3YmNiZDcyZWI5NzVmZmNkMjcwOTg0OTc4NDRiNTc0MjVmMmM5YWY4ZWE1NmNiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-611868230\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-905397365 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|o3CnvmCBSSL98ViBJdgJN5so8aDe5dXi7VGRA2SAZJYu2BSuxQ6yHeXfJ63N|$2y$12$4xmFIOy4V2zK3g4n9iqTEuh93mpXc3HCy/DDmxWzDBRr3EJiAeNFG</span>\"\n  \"<span class=sf-dump-key>_ga_FSKD72EXSB</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jbzeKOEqSud6pSChnp17wAvUgUTGQrZr4mytzMah</span>\"\n  \"<span class=sf-dump-key>shaqi_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p1qIhFLYEVd1Cf5BIAVCAxzvLAKTjzJ7cjgyhoKx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-905397365\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-15282845 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 15 Jul 2025 23:05:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6IlJxV2RFWnBjMFYzenhycVlldUhzcHc9PSIsInZhbHVlIjoiV0pNelR6TVBWcUpsZXJLS0EvTFZsUXk5UzAxN3pGUFdTZHN3V1ZHZW5zU0Q5dDNNbjg3V3VRZUQ5OFIwOURLWnBqdm4rNnF2YTlId0RwTHBXcUw2Y1ZxbGl0am8xM1AxcFBzN3d6Q3hESXQ2V0pqWWJGNDJnekF5TXJjUWRzVzEiLCJtYWMiOiJmZmY0NWE5MWE2YWQ1M2ZlYzE2ZjIxNzM2YjVhMzBjYWM4YzA1YzY2NmZhMGZiZTExYzk3MGY4YzhiMDlhMmIxIiwidGFnIjoiIn0%3D; expires=Wed, 16 Jul 2025 01:05:45 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">shaqi_session=eyJpdiI6IjJxUkZseDAzL0FJZWdIZUFyOGM0TkE9PSIsInZhbHVlIjoiTDY2aGZTR0Q4aGNxbXBiRnBuUWlmRWt6cmdIQXA5QUZXWklYMm5NTFpzbExWSzJTT21YVUYrZ3drbDI2ajJyM0RlazZxK1ozZEVUcHNwQ2RkbzZWbjE4YTd4Vk5lWXVhZ3RTVlNqeUlVc3A2VXpJejU5ZVZoM1RJblVjYzhwcUgiLCJtYWMiOiJhZGQxZDBiMmRmZDkzMWZkZWVmZDIwNThlZTQ5MjdmYTUyYjA4ZGM2N2Y4NTY0M2U1MjAzODRiYjRmOTY3ODg2IiwidGFnIjoiIn0%3D; expires=Wed, 16 Jul 2025 01:05:45 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6IlJxV2RFWnBjMFYzenhycVlldUhzcHc9PSIsInZhbHVlIjoiV0pNelR6TVBWcUpsZXJLS0EvTFZsUXk5UzAxN3pGUFdTZHN3V1ZHZW5zU0Q5dDNNbjg3V3VRZUQ5OFIwOURLWnBqdm4rNnF2YTlId0RwTHBXcUw2Y1ZxbGl0am8xM1AxcFBzN3d6Q3hESXQ2V0pqWWJGNDJnekF5TXJjUWRzVzEiLCJtYWMiOiJmZmY0NWE5MWE2YWQ1M2ZlYzE2ZjIxNzM2YjVhMzBjYWM4YzA1YzY2NmZhMGZiZTExYzk3MGY4YzhiMDlhMmIxIiwidGFnIjoiIn0%3D; expires=Wed, 16-Jul-2025 01:05:45 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">shaqi_session=eyJpdiI6IjJxUkZseDAzL0FJZWdIZUFyOGM0TkE9PSIsInZhbHVlIjoiTDY2aGZTR0Q4aGNxbXBiRnBuUWlmRWt6cmdIQXA5QUZXWklYMm5NTFpzbExWSzJTT21YVUYrZ3drbDI2ajJyM0RlazZxK1ozZEVUcHNwQ2RkbzZWbjE4YTd4Vk5lWXVhZ3RTVlNqeUlVc3A2VXpJejU5ZVZoM1RJblVjYzhwcUgiLCJtYWMiOiJhZGQxZDBiMmRmZDkzMWZkZWVmZDIwNThlZTQ5MjdmYTUyYjA4ZGM2N2Y4NTY0M2U1MjAzODRiYjRmOTY3ODg2IiwidGFnIjoiIn0%3D; expires=Wed, 16-Jul-2025 01:05:45 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-15282845\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1513809255 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jbzeKOEqSud6pSChnp17wAvUgUTGQrZr4mytzMah</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"60 characters\">https://goalconversion.gc/gc/lgn/portfolio/portfolios/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1513809255\", {\"maxDepth\":0})</script>\n"}}