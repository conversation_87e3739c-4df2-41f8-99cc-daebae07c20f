<?php if(
    isset($options['choices'])
    && (is_array($options['choices']) || $options['choices'] instanceof \Illuminate\Support\Collection)
): ?>
    <?php if(count($options['choices']) < 50): ?>
        <div class="mb-3">
            <div class="input-icon">
                <input
                    type="text"
                    id="search-category-input-<?php echo e($inputSearchId = mt_rand()); ?>"
                    class="form-control"
                    placeholder="<?php echo e(trans('core/base::forms.search_input_placeholder')); ?>"
                    onkeyup="filter_categories_<?php echo e($inputSearchId); ?>(<?php echo e($inputSearchId); ?>)"
                    formnovalidate
                />
                <span class="input-icon-addon">
                  <?php if (isset($component)) { $__componentOriginal5f07dace3121fc3466b5a5cd4d4b952a = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5f07dace3121fc3466b5a5cd4d4b952a = $attributes; } ?>
<?php $component = Shaqi\Icon\View\Components\Icon::resolve(['name' => 'ti ti-search'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('core::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Shaqi\Icon\View\Components\Icon::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5f07dace3121fc3466b5a5cd4d4b952a)): ?>
<?php $attributes = $__attributesOriginal5f07dace3121fc3466b5a5cd4d4b952a; ?>
<?php unset($__attributesOriginal5f07dace3121fc3466b5a5cd4d4b952a); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5f07dace3121fc3466b5a5cd4d4b952a)): ?>
<?php $component = $__componentOriginal5f07dace3121fc3466b5a5cd4d4b952a; ?>
<?php unset($__componentOriginal5f07dace3121fc3466b5a5cd4d4b952a); ?>
<?php endif; ?>
                </span>
            </div>
        </div>

        <div data-bb-toggle="tree-checkboxes" class="tree-categories-list-<?php echo e($inputSearchId); ?>">
            <?php echo $__env->make('core/base::forms.partials.tree-categories-checkbox-options', [
                'categories' => $options['choices'],
                'selected' => $options['selected'],
                'currentId' => null,
                'name' => $name,
            ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>

        <script>
            function filter_categories_<?php echo e($inputSearchId); ?>(inputSearchId) {
                const searchInput = document.getElementById('search-category-input-' + inputSearchId).value.toLowerCase();
                const categories = document.querySelectorAll('.tree-categories-list-' + inputSearchId + ' label');

                categories.forEach(category => {
                    const text = category.textContent.toLowerCase();
                    category.style.display = text.includes(searchInput) ? '' : 'none';
                });
            }
        </script>
    <?php else: ?>
        <?php if (isset($component)) { $__componentOriginald8f3cab0e02bd6920e9589a31228d9ca = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald8f3cab0e02bd6920e9589a31228d9ca = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'a74ad8dfacd4f985eb3977517615ce25::form.select','data' => ['multiple' => true,'name' => $name,'dataBbToggle' => 'tree-categories-select','dataPlaceholder' => trans('core/base::forms.select_placeholder')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('core::form.select'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['multiple' => true,'name' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($name),'data-bb-toggle' => 'tree-categories-select','data-placeholder' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(trans('core/base::forms.select_placeholder'))]); ?>
            <?php echo $__env->make('core/base::forms.partials.tree-categories-select-options', [
                'categories' => $options['choices'],
                'selected' => $options['selected'],
                'currentId' => null,
                'name' => $name,
                'indent' => null,
            ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald8f3cab0e02bd6920e9589a31228d9ca)): ?>
<?php $attributes = $__attributesOriginald8f3cab0e02bd6920e9589a31228d9ca; ?>
<?php unset($__attributesOriginald8f3cab0e02bd6920e9589a31228d9ca); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald8f3cab0e02bd6920e9589a31228d9ca)): ?>
<?php $component = $__componentOriginald8f3cab0e02bd6920e9589a31228d9ca; ?>
<?php unset($__componentOriginald8f3cab0e02bd6920e9589a31228d9ca); ?>
<?php endif; ?>
    <?php endif; ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\goalconversion\platform/core/base/resources/views/forms/fields/tree-categories.blade.php ENDPATH**/ ?>