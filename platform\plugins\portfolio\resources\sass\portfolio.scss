// Portfolio Plugin Styles

.portfolio-form {
    .gallery-images-wrapper {
        .default-placeholder-gallery-image {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            
            &:hover {
                border-color: #007bff;
                background-color: #f8f9fa;
            }
        }

        .list-gallery-media-images {
            .gallery-image-item-handler {
                position: relative;
                
                .custom-image-box {
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    overflow: hidden;
                    
                    .preview-image-wrapper {
                        position: relative;
                        
                        .preview-image {
                            width: 100%;
                            height: 120px;
                            object-fit: cover;
                        }
                        
                        .image-picker-backdrop {
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            background: rgba(0, 0, 0, 0.5);
                            opacity: 0;
                            transition: opacity 0.3s ease;
                        }
                    }
                    
                    .btn-remove-gallery-image {
                        position: absolute;
                        top: 5px;
                        right: 5px;
                        z-index: 10;
                        padding: 0.25rem 0.5rem;
                        font-size: 0.75rem;
                    }
                    
                    &:hover {
                        .image-picker-backdrop {
                            opacity: 1;
                        }
                    }
                }
            }
            
            .gallery-image-placeholder {
                height: 120px;
                background: #f8f9fa;
                border: 2px dashed #007bff;
                border-radius: 8px;
            }
        }
    }

    .repeater-field {
        .repeater-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            position: relative;
            
            .btn-remove-repeater-item {
                position: absolute;
                top: 10px;
                right: 10px;
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;
            }
        }
        
        .btn-add-repeater-item {
            margin-top: 0.5rem;
        }
    }
}

// Admin specific styles
.main-form-wrapper {
    .portfolio-form {
        .form-group {
            margin-bottom: 1.5rem;
        }
    }
}
