{"__meta": {"id": "X3d92bf37dbcb6bde9ae3d8d0d25d6fd2", "datetime": "2025-07-15 22:59:44", "utime": 1752620384.16344, "method": "POST", "uri": "/gc/lgn/media/files/upload", "ip": "127.0.0.1"}, "php": {"version": "8.3.17", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752620380.317403, "end": 1752620384.163473, "duration": 3.846069812774658, "duration_str": "3.85s", "measures": [{"label": "Booting", "start": 1752620380.317403, "relative_start": 0, "end": **********.663333, "relative_end": **********.663333, "duration": 1.3459298610687256, "duration_str": "1.35s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.663357, "relative_start": 1.3459539413452148, "end": 1752620384.163476, "relative_end": 3.0994415283203125e-06, "duration": 2.5001189708709717, "duration_str": "2.5s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48261080, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST gc/lgn/media/files/upload", "middleware": "web, core, auth", "permission": "media.index", "as": "media.files.upload", "controller": "Shaqi\\Media\\Http\\Controllers\\MediaFileController@postUpload", "namespace": "Shaqi\\Media\\Http\\Controllers", "prefix": "gc/lgn/media/files", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fmedia%2Fsrc%2FHttp%2FControllers%2FMediaFileController.php&line=22\" onclick=\"\">vendor/shaqi/platform/media/src/Http/Controllers/MediaFileController.php:22-55</a>"}, "queries": {"nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.09731000000000001, "accumulated_duration_str": "97.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.907967, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 0, "width_percent": 0.658}, {"sql": "select exists(select * from `media_files` where `name` = 'north-gate-animal-1' and `folder_id` = '0') as `exists`", "type": "query", "params": [], "bindings": ["north-gate-animal-1", "0"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/shaqi/platform/media/src/Models/MediaFile.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\media\\src\\Models\\MediaFile.php", "line": 250}, {"index": 12, "namespace": null, "name": "vendor/shaqi/platform/media/src/RvMedia.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\media\\src\\RvMedia.php", "line": 513}, {"index": 14, "namespace": null, "name": "vendor/shaqi/platform/media/src/Http/Controllers/MediaFileController.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\media\\src\\Http\\Controllers\\MediaFileController.php", "line": 26}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.360842, "duration": 0.08276, "duration_str": "82.76ms", "memory": 0, "memory_str": null, "filename": "MediaFile.php:250", "source": {"index": 11, "namespace": null, "name": "vendor/shaqi/platform/media/src/Models/MediaFile.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\media\\src\\Models\\MediaFile.php", "line": 250}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fmedia%2Fsrc%2FModels%2FMediaFile.php&line=250", "ajax": false, "filename": "MediaFile.php", "line": "250"}, "connection": "goalconversion", "explain": null, "start_percent": 0.658, "width_percent": 85.048}, {"sql": "insert into `media_files` (`name`, `url`, `alt`, `size`, `mime_type`, `folder_id`, `user_id`, `options`, `visibility`, `updated_at`, `created_at`) values ('north-gate-animal-1', 'north-gate-animal-1.jpg', 'north-gate-animal-1', 21227, 'image/jpeg', '0', 1, '[]', 'public', '2025-07-15 22:59:43', '2025-07-15 22:59:43')", "type": "query", "params": [], "bindings": ["north-gate-animal-1", "north-gate-animal-1.jpg", "north-gate-animal-1", 21227, "image/jpeg", "0", 1, "[]", "public", "2025-07-15 22:59:43", "2025-07-15 22:59:43"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/media/src/RvMedia.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\media\\src\\RvMedia.php", "line": 586}, {"index": 17, "namespace": null, "name": "vendor/shaqi/platform/media/src/Http/Controllers/MediaFileController.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\media\\src\\Http\\Controllers\\MediaFileController.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.839258, "duration": 0.01391, "duration_str": "13.91ms", "memory": 0, "memory_str": null, "filename": "RvMedia.php:586", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/media/src/RvMedia.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\media\\src\\RvMedia.php", "line": 586}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fmedia%2Fsrc%2FRvMedia.php&line=586", "ajax": false, "filename": "RvMedia.php", "line": "586"}, "connection": "goalconversion", "explain": null, "start_percent": 85.705, "width_percent": 14.295}]}, "models": {"data": {"Shaqi\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "jbzeKOEqSud6pSChnp17wAvUgUTGQrZr4mytzMah", "_previous": "array:1 [\n  \"url\" => \"https://goalconversion.gc\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"path_info": "/gc/lgn/media/files/upload", "status_code": "<pre class=sf-dump id=sf-dump-624382724 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-624382724\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-306542082 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-306542082\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-737618234 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jbzeKOEqSud6pSChnp17wAvUgUTGQrZr4mytzMah</span>\"\n  \"<span class=sf-dump-key>folder_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>view_in</span>\" => \"<span class=sf-dump-str title=\"9 characters\">all_media</span>\"\n  \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"9 characters\">undefined</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-737618234\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-445959405 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">goalconversion.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">130711</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryUHhhAXm8PsRIbBQb</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">https://goalconversion.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"60 characters\">https://goalconversion.gc/gc/lgn/portfolio/portfolios/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1370 characters\">_ga=GA1.1.996387623.1750873576; cookie_for_consent=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjlFUmdOdDJTZlc2S0NuaFZHV01wWFE9PSIsInZhbHVlIjoiM3JYNzlHNUxRbDYvT2xLc0lxeTI0alRqcThNaVRrRXk4N1RGdEZGLy9QVnM4ZmFUZzdhckN1bCtIeGc0UDgzRTdNQkNtN0dlSHU2YmVURHIrMDA5YnlzeG82Yzh6WnhjcTlENEVwazRCRzgvdE9naDVjWG5kNzFTYkgySWNsSlk1OGhyS1pNai9ReFdleW5QZFdndHBQbHZYck13cGc0ZjZvUHNHTk1KOVcrVFExRURSL2lkaHp4cXMwUlc0RTViRnN5akMzU25RTDQzeXNoWG9XU2hZUVE5aG5xVUh2bWJiWWV3a3pmdGFPdz0iLCJtYWMiOiIxYTk2ZTQ1ODRjMTQ3OTc0OWVlZmQyY2JkN2IyMzdlODEzYWZmMTkyZWJjMThlNWRhZmM2NjlmMTdiODIzYmFmIiwidGFnIjoiIn0%3D; _ga_FSKD72EXSB=GS2.1.s1752619353$o34$g1$t1752620286$j60$l0$h0; XSRF-TOKEN=eyJpdiI6ImljM1lBalNsaFduRE14bmlIVURUK0E9PSIsInZhbHVlIjoiS3FnYk16azkwY2k3RXM1ZFR5VDg3TjdCWEttRjMzeHpVQ0RFWm5SSWNGazNJM2poL1VJNjMxS3pWcDNJRjlKNkliSjdHRVcySkFBdFpVdDJTV0w5NGNYbUtGdEMvTklQWGlnZFQxTE9MODhFNjkwQUMxYkRwekZLSTczejFHWHUiLCJtYWMiOiI2YmU0ZTllNDdlYTE0NTk0YzQ3YzM0NGJjOTZiYjk1ZWExN2Y1NjkwZTgzNzhhNDVlM2VmNDgyZjE0Yjg5ZmZiIiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6InlKV0ZMN3VpcWxhSkRiYnRpcEhLUlE9PSIsInZhbHVlIjoibWdsSXlGalZWNU5XcnBNZ0dYMnpzTmZDMXlvTnFoc3RzUHlPUlZIMDVGcTFzM1JtNmcvTWRKc2w3dy9yWFlaMHk2ZVpFS0xZRHMzVDBjWHJyenZSUFRpeWkrTng5MkpHTWwyMHYyeXlXcGg2d010NWIzVWFaNXFudGdYNEhEWHYiLCJtYWMiOiJjMDkxNDI1OTRmNTcyOGEwYTlmYjMwMDFjMThkYjIxOGJhZjI2N2U3YWNkMmM2NmNlNzRhODk0MzNjODBjYjljIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-445959405\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-764398331 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|o3CnvmCBSSL98ViBJdgJN5so8aDe5dXi7VGRA2SAZJYu2BSuxQ6yHeXfJ63N|$2y$12$4xmFIOy4V2zK3g4n9iqTEuh93mpXc3HCy/DDmxWzDBRr3EJiAeNFG</span>\"\n  \"<span class=sf-dump-key>_ga_FSKD72EXSB</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jbzeKOEqSud6pSChnp17wAvUgUTGQrZr4mytzMah</span>\"\n  \"<span class=sf-dump-key>shaqi_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p1qIhFLYEVd1Cf5BIAVCAxzvLAKTjzJ7cjgyhoKx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-764398331\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1947931736 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 15 Jul 2025 22:59:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6IklSdEdmRzFzcENzdjQxa0xGQWVYN0E9PSIsInZhbHVlIjoiMHMwMmJLOWhYZTA2R3NOd1BjSW94MldTWlUxVVk4OWE0NFJQOFBvd3F3QnNhYXpuMkRKRzBoa3dnU2pxQ0RDSDNZN2c0WVRaZ0VKcHFVeGJMNTYrTjRHTTE5Y0JyY1VCL2M0UHNpQm1XM0NoNkpmSmpyaUlCb0w5SlRzZWxQRnMiLCJtYWMiOiI5MWNmYzQ3YTY3YTFlZDQxZGM3Y2ExNjhiMGY2MWVhYTFmMzg5ZDc1NDQ4ZmFjNWQ0NTk4OTU4NjkyNWEzN2RkIiwidGFnIjoiIn0%3D; expires=Wed, 16 Jul 2025 00:59:44 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">shaqi_session=eyJpdiI6IjhXQmY5b3d4MmhOKzJNRXRwL3ZvS2c9PSIsInZhbHVlIjoibjJiZ25VVU53WjIrc1BXVUVGSWtBMXUzdmpKZ1orV2s4VXJ2NVJYVTkwVjR6MmpyQURaS3pXOFVGcVpWekY1OHhUNzE2OWhudjdoTFBFWUk2SUx6TG5WMFB1MklycGNlQm1sSmZnTTcvTnB2M0FjLzBRYmFKZjNXbWY0ZVE1VVQiLCJtYWMiOiI3NzRjMGJhODJlNmE0NDA4MDYwMmExNzAzYjNiZjUzMmVjNjYwNjIxMDgzNjhhZjgyMDdlMjY0Yjk0YzQxYTcwIiwidGFnIjoiIn0%3D; expires=Wed, 16 Jul 2025 00:59:44 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6IklSdEdmRzFzcENzdjQxa0xGQWVYN0E9PSIsInZhbHVlIjoiMHMwMmJLOWhYZTA2R3NOd1BjSW94MldTWlUxVVk4OWE0NFJQOFBvd3F3QnNhYXpuMkRKRzBoa3dnU2pxQ0RDSDNZN2c0WVRaZ0VKcHFVeGJMNTYrTjRHTTE5Y0JyY1VCL2M0UHNpQm1XM0NoNkpmSmpyaUlCb0w5SlRzZWxQRnMiLCJtYWMiOiI5MWNmYzQ3YTY3YTFlZDQxZGM3Y2ExNjhiMGY2MWVhYTFmMzg5ZDc1NDQ4ZmFjNWQ0NTk4OTU4NjkyNWEzN2RkIiwidGFnIjoiIn0%3D; expires=Wed, 16-Jul-2025 00:59:44 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">shaqi_session=eyJpdiI6IjhXQmY5b3d4MmhOKzJNRXRwL3ZvS2c9PSIsInZhbHVlIjoibjJiZ25VVU53WjIrc1BXVUVGSWtBMXUzdmpKZ1orV2s4VXJ2NVJYVTkwVjR6MmpyQURaS3pXOFVGcVpWekY1OHhUNzE2OWhudjdoTFBFWUk2SUx6TG5WMFB1MklycGNlQm1sSmZnTTcvTnB2M0FjLzBRYmFKZjNXbWY0ZVE1VVQiLCJtYWMiOiI3NzRjMGJhODJlNmE0NDA4MDYwMmExNzAzYjNiZjUzMmVjNjYwNjIxMDgzNjhhZjgyMDdlMjY0Yjk0YzQxYTcwIiwidGFnIjoiIn0%3D; expires=Wed, 16-Jul-2025 00:59:44 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1947931736\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1402513807 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jbzeKOEqSud6pSChnp17wAvUgUTGQrZr4mytzMah</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">https://goalconversion.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1402513807\", {\"maxDepth\":0})</script>\n"}}