{"__meta": {"id": "X2fc454dd968ecf29dfe9bc47b95c08be", "datetime": "2025-07-15 23:05:35", "utime": **********.983916, "method": "GET", "uri": "/gc/lgn/menu-items-count", "ip": "127.0.0.1"}, "php": {"version": "8.3.17", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752620734.477981, "end": **********.98396, "duration": 1.5059788227081299, "duration_str": "1.51s", "measures": [{"label": "Booting", "start": 1752620734.477981, "relative_start": 0, "end": **********.834081, "relative_end": **********.834081, "duration": 1.3560998439788818, "duration_str": "1.36s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.834113, "relative_start": 1.3561317920684814, "end": **********.983966, "relative_end": 6.198883056640625e-06, "duration": 0.14985322952270508, "duration_str": "150ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 41120688, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET gc/lgn/menu-items-count", "middleware": "web, core, auth", "permission": false, "as": "menu-items-count", "controller": "Shaqi\\Base\\Http\\Controllers\\SystemController@getMenuItemsCount", "namespace": "Shaqi\\Base\\Http\\Controllers", "prefix": "/gc/lgn", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FHttp%2FControllers%2FSystemController.php&line=38\" onclick=\"\">vendor/shaqi/platform/base/src/Http/Controllers/SystemController.php:38-45</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.011460000000000001, "accumulated_duration_str": "11.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.9509299, "duration": 0.011460000000000001, "duration_str": "11.46ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"Shaqi\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "jbzeKOEqSud6pSChnp17wAvUgUTGQrZr4mytzMah", "_previous": "array:1 [\n  \"url\" => \"https://goalconversion.gc/gc/lgn/portfolio/portfolios/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"path_info": "/gc/lgn/menu-items-count", "status_code": "<pre class=sf-dump id=sf-dump-1322577036 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1322577036\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-695070257 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-695070257\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-781505721 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-781505721\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-197353928 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">goalconversion.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ijg2aFpwSnRoOVg4THhMQTZabmx1eXc9PSIsInZhbHVlIjoiKzNNQ1JhaTFDM2lXc2c2L3dxVjJLdEFFYXRjakdZMDg1bWZFMElsbURYeGVXMXUwQ2NZdDZLYTNaVmpkenFGUms2eTE1V04wamczN2JGYWlheUZhaE0wMXhHa3NZRGNzRzFPd2hjbDVqOEFsVy9UbU1pbHdxREltOHF3dzhwTmciLCJtYWMiOiI3M2QyMWExZTM4ZGQ1ZThlN2IzM2YzNTQ4M2ViNjc5ZTAyZjFlMDM0OTUxMTJhNDg0OTY1MDJmNGZmYWE5MDM5IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"60 characters\">https://goalconversion.gc/gc/lgn/portfolio/portfolios/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1370 characters\">_ga=GA1.1.996387623.1750873576; cookie_for_consent=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjlFUmdOdDJTZlc2S0NuaFZHV01wWFE9PSIsInZhbHVlIjoiM3JYNzlHNUxRbDYvT2xLc0lxeTI0alRqcThNaVRrRXk4N1RGdEZGLy9QVnM4ZmFUZzdhckN1bCtIeGc0UDgzRTdNQkNtN0dlSHU2YmVURHIrMDA5YnlzeG82Yzh6WnhjcTlENEVwazRCRzgvdE9naDVjWG5kNzFTYkgySWNsSlk1OGhyS1pNai9ReFdleW5QZFdndHBQbHZYck13cGc0ZjZvUHNHTk1KOVcrVFExRURSL2lkaHp4cXMwUlc0RTViRnN5akMzU25RTDQzeXNoWG9XU2hZUVE5aG5xVUh2bWJiWWV3a3pmdGFPdz0iLCJtYWMiOiIxYTk2ZTQ1ODRjMTQ3OTc0OWVlZmQyY2JkN2IyMzdlODEzYWZmMTkyZWJjMThlNWRhZmM2NjlmMTdiODIzYmFmIiwidGFnIjoiIn0%3D; _ga_FSKD72EXSB=GS2.1.s1752619353$o34$g1$t1752620286$j60$l0$h0; XSRF-TOKEN=eyJpdiI6Ijg2aFpwSnRoOVg4THhMQTZabmx1eXc9PSIsInZhbHVlIjoiKzNNQ1JhaTFDM2lXc2c2L3dxVjJLdEFFYXRjakdZMDg1bWZFMElsbURYeGVXMXUwQ2NZdDZLYTNaVmpkenFGUms2eTE1V04wamczN2JGYWlheUZhaE0wMXhHa3NZRGNzRzFPd2hjbDVqOEFsVy9UbU1pbHdxREltOHF3dzhwTmciLCJtYWMiOiI3M2QyMWExZTM4ZGQ1ZThlN2IzM2YzNTQ4M2ViNjc5ZTAyZjFlMDM0OTUxMTJhNDg0OTY1MDJmNGZmYWE5MDM5IiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6IkxMT2NJUlUvamF6Y3JPQUxzNlZKZGc9PSIsInZhbHVlIjoiSGE4dWFROGhaZno5eDRrdlZDY3FkNGJaWkwyZ05FenJ2anpDUXRCUzRocjU0M2FpMm5Lb25kd0FFVE5GY255Y0hSYnlxRXV0Z2VTRk9uRkxDaldBWnRIamQvT0ZpRUNkVHNKMEIwMDcxdUlyMWdkMktFSkk4c2plMitsMHIxNlUiLCJtYWMiOiI4M2M1OTRiZmQ4MGZjNmE1Y2E3YmNiZDcyZWI5NzVmZmNkMjcwOTg0OTc4NDRiNTc0MjVmMmM5YWY4ZWE1NmNiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-197353928\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-453630914 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|o3CnvmCBSSL98ViBJdgJN5so8aDe5dXi7VGRA2SAZJYu2BSuxQ6yHeXfJ63N|$2y$12$4xmFIOy4V2zK3g4n9iqTEuh93mpXc3HCy/DDmxWzDBRr3EJiAeNFG</span>\"\n  \"<span class=sf-dump-key>_ga_FSKD72EXSB</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jbzeKOEqSud6pSChnp17wAvUgUTGQrZr4mytzMah</span>\"\n  \"<span class=sf-dump-key>shaqi_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p1qIhFLYEVd1Cf5BIAVCAxzvLAKTjzJ7cjgyhoKx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-453630914\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1906349457 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 15 Jul 2025 23:05:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6Ik9Ia0dYN3lSaGwvcmdVUmQxa0lta0E9PSIsInZhbHVlIjoiQXNLbFFhS1dya3dvTjlYMTE5UzdzNVFnM1RaNHZzZVozTDNhUGRiVXdrVVZKc0lZUmxxUnY3K0FFMlZiUENWamdRb05pcTJaaHdqRlJpNmpxV2hBTXV2NHE2ejhQQ2dMU09MbUZZb2lIVnNMYWJsTDRBTjI1M2tLYkJiUEc5YkYiLCJtYWMiOiI2YTg5YmRiNWRiM2ExMjZmZmVkZTU1OGUwMGQ1NWU5NmM0YzU2ZWFjYTBiMGRmMzdkYjkxYzUxYTBmYzVlYTdjIiwidGFnIjoiIn0%3D; expires=Wed, 16 Jul 2025 01:05:35 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">shaqi_session=eyJpdiI6ImxyOEZHK0UwVU1aR1JSa0k4ci9DM0E9PSIsInZhbHVlIjoiSjMyam9kSERjdjVRNi92bnZKK1BBbUJzOCtEMVVHRTdGd3Y1WTBXSWQ4WlQ1OGhWdVZ2dWdTTklwOG0zSnlsdUlBOXZWTTd4MEtnck9EdGtWRnIzdkRRQTM5RW1nNUtvbmtzMGVsYjRseEhVa2hzUEJmYk1tRGlpa0ZZaU5yR2kiLCJtYWMiOiIwZmU1NGMxNThlNmIxNDM3YTAzZmY2MzY1OTA0MzRhY2FkYjQ0ZjMzZTE1ZjBhOWU4YWZmM2M3YjcxOTc2NTAxIiwidGFnIjoiIn0%3D; expires=Wed, 16 Jul 2025 01:05:35 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6Ik9Ia0dYN3lSaGwvcmdVUmQxa0lta0E9PSIsInZhbHVlIjoiQXNLbFFhS1dya3dvTjlYMTE5UzdzNVFnM1RaNHZzZVozTDNhUGRiVXdrVVZKc0lZUmxxUnY3K0FFMlZiUENWamdRb05pcTJaaHdqRlJpNmpxV2hBTXV2NHE2ejhQQ2dMU09MbUZZb2lIVnNMYWJsTDRBTjI1M2tLYkJiUEc5YkYiLCJtYWMiOiI2YTg5YmRiNWRiM2ExMjZmZmVkZTU1OGUwMGQ1NWU5NmM0YzU2ZWFjYTBiMGRmMzdkYjkxYzUxYTBmYzVlYTdjIiwidGFnIjoiIn0%3D; expires=Wed, 16-Jul-2025 01:05:35 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">shaqi_session=eyJpdiI6ImxyOEZHK0UwVU1aR1JSa0k4ci9DM0E9PSIsInZhbHVlIjoiSjMyam9kSERjdjVRNi92bnZKK1BBbUJzOCtEMVVHRTdGd3Y1WTBXSWQ4WlQ1OGhWdVZ2dWdTTklwOG0zSnlsdUlBOXZWTTd4MEtnck9EdGtWRnIzdkRRQTM5RW1nNUtvbmtzMGVsYjRseEhVa2hzUEJmYk1tRGlpa0ZZaU5yR2kiLCJtYWMiOiIwZmU1NGMxNThlNmIxNDM3YTAzZmY2MzY1OTA0MzRhY2FkYjQ0ZjMzZTE1ZjBhOWU4YWZmM2M3YjcxOTc2NTAxIiwidGFnIjoiIn0%3D; expires=Wed, 16-Jul-2025 01:05:35 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1906349457\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jbzeKOEqSud6pSChnp17wAvUgUTGQrZr4mytzMah</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"60 characters\">https://goalconversion.gc/gc/lgn/portfolio/portfolios/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}