@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">{{ $portfolio->name }}</h4>
                </div>
                <div class="card-body">
                    @if($portfolio->image)
                        <div class="mb-3">
                            <img src="{{ RvMedia::getImageUrl($portfolio->image) }}" alt="{{ $portfolio->name }}" class="img-fluid rounded">
                        </div>
                    @endif

                    @if($portfolio->description)
                        <div class="mb-3">
                            <h5>{{ trans('plugins/portfolio::portfolios.form.description') }}</h5>
                            <p>{{ $portfolio->description }}</p>
                        </div>
                    @endif

                    @if($portfolio->content)
                        <div class="mb-3">
                            <h5>{{ trans('plugins/portfolio::portfolios.form.content') }}</h5>
                            <div class="content">
                                {!! BaseHelper::clean($portfolio->content) !!}
                            </div>
                        </div>
                    @endif

                    @if($portfolio->features && count($portfolio->features) > 0)
                        <div class="mb-3">
                            <h5>{{ trans('plugins/portfolio::portfolios.form.features') }}</h5>
                            <ul class="list-unstyled">
                                @foreach($portfolio->features as $feature)
                                    @if(is_array($feature) && isset($feature['feature']))
                                        <li><i class="fas fa-check text-success me-2"></i>{{ $feature['feature'] }}</li>
                                    @elseif(is_string($feature))
                                        <li><i class="fas fa-check text-success me-2"></i>{{ $feature }}</li>
                                    @endif
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    @if($portfolio->gallery && count($portfolio->gallery) > 0)
                        <div class="mb-3">
                            <h5>{{ trans('plugins/portfolio::portfolios.form.gallery') }}</h5>
                            <div class="row">
                                @foreach($portfolio->gallery as $image)
                                    <div class="col-md-3 col-sm-4 col-6 mb-3">
                                        <img src="{{ RvMedia::getImageUrl($image, 'thumb') }}" alt="Gallery Image" class="img-fluid rounded">
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">{{ trans('core/base::forms.information') }}</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>{{ trans('core/base::tables.status') }}:</strong>
                        <span class="badge bg-{{ $portfolio->status->getValue() === 'published' ? 'success' : 'warning' }}">
                            {{ $portfolio->status->label() }}
                        </span>
                    </div>

                    @if($portfolio->website_link)
                        <div class="mb-3">
                            <strong>{{ trans('plugins/portfolio::portfolios.form.website_link') }}:</strong>
                            <a href="{{ $portfolio->website_link }}" target="_blank" class="d-block">
                                {{ $portfolio->website_link }}
                            </a>
                        </div>
                    @endif

                    @if($portfolio->logo)
                        <div class="mb-3">
                            <strong>{{ trans('plugins/portfolio::portfolios.form.logo') }}:</strong>
                            <div class="mt-2">
                                <img src="{{ RvMedia::getImageUrl($portfolio->logo, 'thumb') }}" alt="Logo" class="img-fluid" style="max-width: 100px;">
                            </div>
                        </div>
                    @endif

                    @if($portfolio->categories->count() > 0)
                        <div class="mb-3">
                            <strong>{{ trans('plugins/portfolio::portfolios.form.categories') }}:</strong>
                            <div class="mt-1">
                                @foreach($portfolio->categories as $category)
                                    <span class="badge bg-primary me-1">{{ $category->name }}</span>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    @if($portfolio->tags->count() > 0)
                        <div class="mb-3">
                            <strong>{{ trans('plugins/portfolio::portfolios.form.tags') }}:</strong>
                            <div class="mt-1">
                                @foreach($portfolio->tags as $tag)
                                    <span class="badge bg-secondary me-1">{{ $tag->name }}</span>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <div class="mb-3">
                        <strong>{{ trans('core/base::tables.created_at') }}:</strong>
                        <span class="d-block">{{ BaseHelper::formatDate($portfolio->created_at) }}</span>
                    </div>

                    <div class="mb-3">
                        <strong>{{ trans('core/base::tables.updated_at') }}:</strong>
                        <span class="d-block">{{ BaseHelper::formatDate($portfolio->updated_at) }}</span>
                    </div>

                    @if($portfolio->is_featured)
                        <div class="mb-3">
                            <span class="badge bg-warning">
                                <i class="fas fa-star me-1"></i>
                                {{ trans('core/base::forms.is_featured') }}
                            </span>
                        </div>
                    @endif
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title">{{ trans('core/base::forms.actions') }}</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('portfolios.edit', $portfolio->id) }}" class="btn btn-primary">
                            <i class="fas fa-edit me-1"></i>
                            {{ trans('core/base::forms.edit') }}
                        </a>
                        
                        <a href="{{ route('portfolios.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>
                            {{ trans('core/base::forms.back') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
