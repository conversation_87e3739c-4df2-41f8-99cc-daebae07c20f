{"__meta": {"id": "X51a2be49c1769fe816972010f1cfa54c", "datetime": "2025-07-15 23:02:09", "utime": 1752620529.084233, "method": "GET", "uri": "/gc/lgn/media/list?view_type=tiles&filter=image&view_in=all_media&sort_by=created_at-desc&folder_id=0&search=&multiple=true&type=*&open_in=modal&load_more_file=false&paged=1&posts_per_page=40", "ip": "127.0.0.1"}, "php": {"version": "8.3.17", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752620526.924994, "end": 1752620529.084288, "duration": 2.1592938899993896, "duration_str": "2.16s", "measures": [{"label": "Booting", "start": 1752620526.924994, "relative_start": 0, "end": **********.682635, "relative_end": **********.682635, "duration": 1.757641077041626, "duration_str": "1.76s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.682669, "relative_start": 1.7576749324798584, "end": 1752620529.084293, "relative_end": 5.0067901611328125e-06, "duration": 0.4016239643096924, "duration_str": "402ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46040304, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 17, "templates": [{"name": "__components::9a1da6c7f662474948fe63691d3a1543", "param_count": null, "params": [], "start": **********.878251, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\storage\\framework\\views/9a1da6c7f662474948fe63691d3a1543.blade.php__components::9a1da6c7f662474948fe63691d3a1543", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fstorage%2Fframework%2Fviews%2F9a1da6c7f662474948fe63691d3a1543.blade.php&line=1", "ajax": false, "filename": "9a1da6c7f662474948fe63691d3a1543.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.971576, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.993448, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.998606, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": 1752620529.003451, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::9a1da6c7f662474948fe63691d3a1543", "param_count": null, "params": [], "start": 1752620529.008555, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\storage\\framework\\views/9a1da6c7f662474948fe63691d3a1543.blade.php__components::9a1da6c7f662474948fe63691d3a1543", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fstorage%2Fframework%2Fviews%2F9a1da6c7f662474948fe63691d3a1543.blade.php&line=1", "ajax": false, "filename": "9a1da6c7f662474948fe63691d3a1543.blade.php", "line": "?"}}, {"name": "__components::9a1da6c7f662474948fe63691d3a1543", "param_count": null, "params": [], "start": 1752620529.013073, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\storage\\framework\\views/9a1da6c7f662474948fe63691d3a1543.blade.php__components::9a1da6c7f662474948fe63691d3a1543", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fstorage%2Fframework%2Fviews%2F9a1da6c7f662474948fe63691d3a1543.blade.php&line=1", "ajax": false, "filename": "9a1da6c7f662474948fe63691d3a1543.blade.php", "line": "?"}}, {"name": "__components::9a1da6c7f662474948fe63691d3a1543", "param_count": null, "params": [], "start": 1752620529.017538, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\storage\\framework\\views/9a1da6c7f662474948fe63691d3a1543.blade.php__components::9a1da6c7f662474948fe63691d3a1543", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fstorage%2Fframework%2Fviews%2F9a1da6c7f662474948fe63691d3a1543.blade.php&line=1", "ajax": false, "filename": "9a1da6c7f662474948fe63691d3a1543.blade.php", "line": "?"}}, {"name": "__components::a460cf1eef36b568db70ad3c1eead84e", "param_count": null, "params": [], "start": 1752620529.023239, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\storage\\framework\\views/a460cf1eef36b568db70ad3c1eead84e.blade.php__components::a460cf1eef36b568db70ad3c1eead84e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fstorage%2Fframework%2Fviews%2Fa460cf1eef36b568db70ad3c1eead84e.blade.php&line=1", "ajax": false, "filename": "a460cf1eef36b568db70ad3c1eead84e.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": 1752620529.029182, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::a460cf1eef36b568db70ad3c1eead84e", "param_count": null, "params": [], "start": 1752620529.035224, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\storage\\framework\\views/a460cf1eef36b568db70ad3c1eead84e.blade.php__components::a460cf1eef36b568db70ad3c1eead84e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fstorage%2Fframework%2Fviews%2Fa460cf1eef36b568db70ad3c1eead84e.blade.php&line=1", "ajax": false, "filename": "a460cf1eef36b568db70ad3c1eead84e.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": 1752620529.040975, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::9a1da6c7f662474948fe63691d3a1543", "param_count": null, "params": [], "start": 1752620529.046325, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\storage\\framework\\views/9a1da6c7f662474948fe63691d3a1543.blade.php__components::9a1da6c7f662474948fe63691d3a1543", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fstorage%2Fframework%2Fviews%2F9a1da6c7f662474948fe63691d3a1543.blade.php&line=1", "ajax": false, "filename": "9a1da6c7f662474948fe63691d3a1543.blade.php", "line": "?"}}, {"name": "__components::a460cf1eef36b568db70ad3c1eead84e", "param_count": null, "params": [], "start": 1752620529.05339, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\storage\\framework\\views/a460cf1eef36b568db70ad3c1eead84e.blade.php__components::a460cf1eef36b568db70ad3c1eead84e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fstorage%2Fframework%2Fviews%2Fa460cf1eef36b568db70ad3c1eead84e.blade.php&line=1", "ajax": false, "filename": "a460cf1eef36b568db70ad3c1eead84e.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": 1752620529.058596, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::9a1da6c7f662474948fe63691d3a1543", "param_count": null, "params": [], "start": 1752620529.064089, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\storage\\framework\\views/9a1da6c7f662474948fe63691d3a1543.blade.php__components::9a1da6c7f662474948fe63691d3a1543", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fstorage%2Fframework%2Fviews%2F9a1da6c7f662474948fe63691d3a1543.blade.php&line=1", "ajax": false, "filename": "9a1da6c7f662474948fe63691d3a1543.blade.php", "line": "?"}}, {"name": "__components::a460cf1eef36b568db70ad3c1eead84e", "param_count": null, "params": [], "start": 1752620529.069707, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\storage\\framework\\views/a460cf1eef36b568db70ad3c1eead84e.blade.php__components::a460cf1eef36b568db70ad3c1eead84e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fstorage%2Fframework%2Fviews%2Fa460cf1eef36b568db70ad3c1eead84e.blade.php&line=1", "ajax": false, "filename": "a460cf1eef36b568db70ad3c1eead84e.blade.php", "line": "?"}}]}, "route": {"uri": "GET gc/lgn/media/list", "middleware": "web, core, auth", "permission": "media.index", "as": "media.list", "controller": "Shaqi\\Media\\Http\\Controllers\\MediaController@getList", "namespace": "Shaqi\\Media\\Http\\Controllers", "prefix": "gc/lgn/media", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fmedia%2Fsrc%2FHttp%2FControllers%2FMediaController.php&line=57\" onclick=\"\">vendor/shaqi/platform/media/src/Http/Controllers/MediaController.php:57-236</a>"}, "queries": {"nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03881, "accumulated_duration_str": "38.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.818873, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 0, "width_percent": 2.036}, {"sql": "(select `media_files`.`id` as `id`, `media_files`.`name` as `name`, `media_files`.`alt` as `alt`, `media_files`.`url` as `url`, `media_files`.`mime_type` as `mime_type`, `media_files`.`size` as `size`, `media_files`.`created_at` as `created_at`, `media_files`.`updated_at` as `updated_at`, `media_files`.`options` as `options`, `media_files`.`folder_id` as `folder_id`, `media_files`.`visibility` as `visibility`, 0 as is_folder, NULL as slug, NULL as parent_id, NULL as color from `media_files` left join `media_folders` on `media_folders`.`id` = `media_files`.`folder_id` where ((`media_files`.`folder_id` = '0' and `media_files`.`deleted_at` is null) or (`media_files`.`deleted_at` is null and `media_folders`.`deleted_at` is not null) or (`media_files`.`deleted_at` is null and `media_folders`.`id` is null)) and (`media_files`.`mime_type` in ('image/png', 'image/jpeg', 'image/gif', 'image/bmp', 'image/svg+xml', 'image/webp', 'image/avif'))) union (select `media_folders`.`id` as `id`, `media_folders`.`name` as `name`, NULL as url, NULL as mime_type, NULL as size, NULL as alt, `media_folders`.`created_at` as `created_at`, `media_folders`.`updated_at` as `updated_at`, NULL as options, NULL as folder_id, NULL as visibility, 1 as is_folder, `media_folders`.`slug` as `slug`, `media_folders`.`parent_id` as `parent_id`, `media_folders`.`color` as `color` from `media_folders` where `parent_id` = '0' and `media_folders`.`deleted_at` is null) order by `is_folder` desc, `created_at` desc limit 40 offset 0", "type": "query", "params": [], "bindings": ["0", "image/png", "image/jpeg", "image/gif", "image/bmp", "image/svg+xml", "image/webp", "image/avif", "0"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "vendor/shaqi/platform/media/src/Repositories/Eloquent/MediaFileRepository.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\media\\src\\Repositories\\Eloquent\\MediaFileRepository.php", "line": 221}, {"index": 17, "namespace": null, "name": "vendor/shaqi/platform/media/src/Repositories/Eloquent/MediaFileRepository.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\media\\src\\Repositories\\Eloquent\\MediaFileRepository.php", "line": 154}, {"index": 18, "namespace": null, "name": "vendor/shaqi/platform/media/src/Http/Controllers/MediaController.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\media\\src\\Http\\Controllers\\MediaController.php", "line": 121}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.882537, "duration": 0.03712, "duration_str": "37.12ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 2.036, "width_percent": 95.645}, {"sql": "select `media_files`.`id` as `id`, `media_files`.`name` as `name`, `media_files`.`alt` as `alt`, `media_files`.`url` as `url`, `media_files`.`mime_type` as `mime_type`, `media_files`.`size` as `size`, `media_files`.`created_at` as `created_at`, `media_files`.`updated_at` as `updated_at`, `media_files`.`options` as `options`, `media_files`.`folder_id` as `folder_id`, `media_files`.`visibility` as `visibility`, 0 as is_folder, NULL as slug, NULL as parent_id, NULL as color from `media_files` where `id` is null and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/shaqi/platform/media/src/Repositories/Eloquent/MediaFileRepository.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\media\\src\\Repositories\\Eloquent\\MediaFileRepository.php", "line": 234}, {"index": 18, "namespace": null, "name": "vendor/shaqi/platform/media/src/Repositories/Eloquent/MediaFileRepository.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\media\\src\\Repositories\\Eloquent\\MediaFileRepository.php", "line": 154}, {"index": 19, "namespace": null, "name": "vendor/shaqi/platform/media/src/Http/Controllers/MediaController.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\media\\src\\Http\\Controllers\\MediaController.php", "line": 121}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9244049, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 97.681, "width_percent": 2.319}]}, "models": {"data": {"Shaqi\\Media\\Models\\MediaFile": {"value": 16, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fmedia%2Fsrc%2FModels%2FMediaFile.php&line=1", "ajax": false, "filename": "MediaFile.php", "line": "?"}}, "Shaqi\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 17, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "jbzeKOEqSud6pSChnp17wAvUgUTGQrZr4mytzMah", "_previous": "array:1 [\n  \"url\" => \"https://goalconversion.gc\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"path_info": "/gc/lgn/media/list", "status_code": "<pre class=sf-dump id=sf-dump-990856965 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-990856965\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-404646404 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>view_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">tiles</span>\"\n  \"<span class=sf-dump-key>filter</span>\" => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  \"<span class=sf-dump-key>view_in</span>\" => \"<span class=sf-dump-str title=\"9 characters\">all_media</span>\"\n  \"<span class=sf-dump-key>sort_by</span>\" => \"<span class=sf-dump-str title=\"15 characters\">created_at-desc</span>\"\n  \"<span class=sf-dump-key>folder_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>multiple</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str>*</span>\"\n  \"<span class=sf-dump-key>open_in</span>\" => \"<span class=sf-dump-str title=\"5 characters\">modal</span>\"\n  \"<span class=sf-dump-key>load_more_file</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  \"<span class=sf-dump-key>paged</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>posts_per_page</span>\" => \"<span class=sf-dump-str title=\"2 characters\">40</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-404646404\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-410893300 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-410893300\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1960431921 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">goalconversion.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Imp3NlhWcmoxK0RKVmFzbVVWaE5uZ1E9PSIsInZhbHVlIjoicWJMKytVeTJ0OWtLQlFJMWZIdjlST3BhMzJuWW5pMk1odGRPUmp6VGI0ZVpFdHVhNVVjQ1NpdGFaTHZicVpMMFlkb0M5U213Mm5QdkdIcXIrZTlxTVBSZUpaUk9wMVYvdnZ0VW9sT3VsR0VjUDRDTkNpeDZBSTNHTXBqVjN3QjYiLCJtYWMiOiI2ZmU2ZTc5OGRlOTE5NzFjYjQ2Y2ZjODNiMDQ4ZmRlMWRiNjU3ZjYyZmZhM2Q2NDM0NmQyMmM3N2ViYjMxZTM2IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"60 characters\">https://goalconversion.gc/gc/lgn/portfolio/portfolios/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1370 characters\">_ga=GA1.1.996387623.1750873576; cookie_for_consent=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjlFUmdOdDJTZlc2S0NuaFZHV01wWFE9PSIsInZhbHVlIjoiM3JYNzlHNUxRbDYvT2xLc0lxeTI0alRqcThNaVRrRXk4N1RGdEZGLy9QVnM4ZmFUZzdhckN1bCtIeGc0UDgzRTdNQkNtN0dlSHU2YmVURHIrMDA5YnlzeG82Yzh6WnhjcTlENEVwazRCRzgvdE9naDVjWG5kNzFTYkgySWNsSlk1OGhyS1pNai9ReFdleW5QZFdndHBQbHZYck13cGc0ZjZvUHNHTk1KOVcrVFExRURSL2lkaHp4cXMwUlc0RTViRnN5akMzU25RTDQzeXNoWG9XU2hZUVE5aG5xVUh2bWJiWWV3a3pmdGFPdz0iLCJtYWMiOiIxYTk2ZTQ1ODRjMTQ3OTc0OWVlZmQyY2JkN2IyMzdlODEzYWZmMTkyZWJjMThlNWRhZmM2NjlmMTdiODIzYmFmIiwidGFnIjoiIn0%3D; _ga_FSKD72EXSB=GS2.1.s1752619353$o34$g1$t1752620286$j60$l0$h0; XSRF-TOKEN=eyJpdiI6Imp3NlhWcmoxK0RKVmFzbVVWaE5uZ1E9PSIsInZhbHVlIjoicWJMKytVeTJ0OWtLQlFJMWZIdjlST3BhMzJuWW5pMk1odGRPUmp6VGI0ZVpFdHVhNVVjQ1NpdGFaTHZicVpMMFlkb0M5U213Mm5QdkdIcXIrZTlxTVBSZUpaUk9wMVYvdnZ0VW9sT3VsR0VjUDRDTkNpeDZBSTNHTXBqVjN3QjYiLCJtYWMiOiI2ZmU2ZTc5OGRlOTE5NzFjYjQ2Y2ZjODNiMDQ4ZmRlMWRiNjU3ZjYyZmZhM2Q2NDM0NmQyMmM3N2ViYjMxZTM2IiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6IjRUTnIvVjNKcjRXNHQ4a1orMFJKSFE9PSIsInZhbHVlIjoiQzBtd1JhQzZ2SHBsWFVBV2wzUTFybFdXTWthR3JsQW9KMkJiT3FFaVczcUcyRFV6MWtBK2xTVWtTV2hhNjBJVGlwSVRLMHBYUGk5REtNQUhFQVBRQWdXOW1vcXU5M0IzelhxVWI2WUpheGRvRTBoL2d3RTU0VnFjYWg5cDRVWXIiLCJtYWMiOiJhZjk1OGEzYWFkNzhjOTdhMzI4MTUzNTYwYTY4YTgzMTY1MWJmMzQ5ZTZlYTdiN2ZhM2EwMGYzZGQ3NGIxMDViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1960431921\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-991274940 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|o3CnvmCBSSL98ViBJdgJN5so8aDe5dXi7VGRA2SAZJYu2BSuxQ6yHeXfJ63N|$2y$12$4xmFIOy4V2zK3g4n9iqTEuh93mpXc3HCy/DDmxWzDBRr3EJiAeNFG</span>\"\n  \"<span class=sf-dump-key>_ga_FSKD72EXSB</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jbzeKOEqSud6pSChnp17wAvUgUTGQrZr4mytzMah</span>\"\n  \"<span class=sf-dump-key>shaqi_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p1qIhFLYEVd1Cf5BIAVCAxzvLAKTjzJ7cjgyhoKx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-991274940\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1567482510 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 15 Jul 2025 23:02:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6InhMMmtuT2RhSlBTNE9GK3hTN2FkOWc9PSIsInZhbHVlIjoibzNXekpuQnoycFF2eWdPWi82NTBMWXlNRzE0NWI1Q2p5bjhkb3dTWk5MRjliWEZMdFQ3NHFzOGFCeW44cURNZXV2QUo0aUkxNmluT1pESTA5VEZFdW13VmExVkdGcThhOHoyUC9reUl1R3ZnNFdVbkY0c2RoMFBpWmlKdjk3OTgiLCJtYWMiOiIzZTM3OThjNjNlNTAwYjU2NWE2ZmY3NGQ1MTY3NTg2NTBjZmJiMzJjMWExMTE0MDdiMDBmNWNlOGE4NjQzNDJjIiwidGFnIjoiIn0%3D; expires=Wed, 16 Jul 2025 01:02:09 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">shaqi_session=eyJpdiI6IkNwY0pQN0R1Y1M0TlRHRmlSd3l2MGc9PSIsInZhbHVlIjoiajBybzRZMmhOTlRSNG9ZUjV3MlFXWFFhajh4bHd1cjZmNU9xRXZadTV3Y3Z3UVlnWkpxU2VRM2pxeHp6ckFGM3ZIUXpZTkw2WkQ4N29jUXN6bFlWd1c3aVZhZlBlR3RCdDFCNG4rSDh0TWIxbldZR1cvSXU3MFQxY0ZDa2U2Qk4iLCJtYWMiOiJlZDg5ZTZiNGNmNzk5MGZlODdkM2Q0NTVjNGY0OTI1MWE5NjRiODVhOTliODliM2EwOTUzMDNiZGJjMmEyMDczIiwidGFnIjoiIn0%3D; expires=Wed, 16 Jul 2025 01:02:09 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6InhMMmtuT2RhSlBTNE9GK3hTN2FkOWc9PSIsInZhbHVlIjoibzNXekpuQnoycFF2eWdPWi82NTBMWXlNRzE0NWI1Q2p5bjhkb3dTWk5MRjliWEZMdFQ3NHFzOGFCeW44cURNZXV2QUo0aUkxNmluT1pESTA5VEZFdW13VmExVkdGcThhOHoyUC9reUl1R3ZnNFdVbkY0c2RoMFBpWmlKdjk3OTgiLCJtYWMiOiIzZTM3OThjNjNlNTAwYjU2NWE2ZmY3NGQ1MTY3NTg2NTBjZmJiMzJjMWExMTE0MDdiMDBmNWNlOGE4NjQzNDJjIiwidGFnIjoiIn0%3D; expires=Wed, 16-Jul-2025 01:02:09 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">shaqi_session=eyJpdiI6IkNwY0pQN0R1Y1M0TlRHRmlSd3l2MGc9PSIsInZhbHVlIjoiajBybzRZMmhOTlRSNG9ZUjV3MlFXWFFhajh4bHd1cjZmNU9xRXZadTV3Y3Z3UVlnWkpxU2VRM2pxeHp6ckFGM3ZIUXpZTkw2WkQ4N29jUXN6bFlWd1c3aVZhZlBlR3RCdDFCNG4rSDh0TWIxbldZR1cvSXU3MFQxY0ZDa2U2Qk4iLCJtYWMiOiJlZDg5ZTZiNGNmNzk5MGZlODdkM2Q0NTVjNGY0OTI1MWE5NjRiODVhOTliODliM2EwOTUzMDNiZGJjMmEyMDczIiwidGFnIjoiIn0%3D; expires=Wed, 16-Jul-2025 01:02:09 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1567482510\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-958256510 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jbzeKOEqSud6pSChnp17wAvUgUTGQrZr4mytzMah</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">https://goalconversion.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-958256510\", {\"maxDepth\":0})</script>\n"}}