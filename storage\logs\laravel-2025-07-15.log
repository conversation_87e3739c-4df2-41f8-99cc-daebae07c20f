[2025-07-15 22:36:31] local.ERROR: View [partials.portfolios-short-code-admin-config] not found. {"exception":"[object] (InvalidArgumentException(code: 0): View [partials.portfolios-short-code-admin-config] not found. at D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php:139)
[stacktrace]
#0 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php(92): Illuminate\\View\\FileViewFinder->findInPaths('partials.portfo...', Array)
#1 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php(76): Illuminate\\View\\FileViewFinder->findNamespacedView('plugins/portfol...')
#2 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\shortcode\\src\\View\\Factory.php(33): Illuminate\\View\\FileViewFinder->find('plugins/portfol...')
#3 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(1062): Shaqi\\Shortcode\\View\\Factory->make('plugins/portfol...', Array, Array)
#4 D:\\laragon\\www\\goalconversion\\platform\\plugins\\portfolio\\src\\Providers\\HookServiceProvider.php(58): view('plugins/portfol...')
#5 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Shaqi\\Portfolio\\Providers\\HookServiceProvider->boot()
#6 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#7 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#8 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#9 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#10 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1124): Illuminate\\Container\\Container->call(Array)
#11 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(898): Illuminate\\Foundation\\Application->bootProvider(Object(Shaqi\\Portfolio\\Providers\\HookServiceProvider))
#12 D:\\laragon\\www\\goalconversion\\platform\\plugins\\portfolio\\src\\Providers\\PortfolioServiceProvider.php(211): Illuminate\\Foundation\\Application->register(Object(Shaqi\\Portfolio\\Providers\\HookServiceProvider))
#13 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1152): Shaqi\\Portfolio\\Providers\\PortfolioServiceProvider->Shaqi\\Portfolio\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application))
#14 D:\\laragon\\www\\goalconversion\\platform\\plugins\\portfolio\\src\\Providers\\PortfolioServiceProvider.php(205): Illuminate\\Foundation\\Application->booted(Object(Closure))
#15 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Shaqi\\Portfolio\\Providers\\PortfolioServiceProvider->boot()
#16 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#18 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#19 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#20 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1124): Illuminate\\Container\\Container->call(Array)
#21 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(898): Illuminate\\Foundation\\Application->bootProvider(Object(Shaqi\\Portfolio\\Providers\\PortfolioServiceProvider))
#22 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\plugin-management\\src\\Services\\PluginService.php(98): Illuminate\\Foundation\\Application->register(Object(Shaqi\\Portfolio\\Providers\\PortfolioServiceProvider))
#23 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\plugin-management\\src\\Commands\\PluginActivateCommand.php(28): Shaqi\\PluginManagement\\Services\\PluginService->activate('portfolio')
#24 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Shaqi\\PluginManagement\\Commands\\PluginActivateCommand->handle(Object(Shaqi\\PluginManagement\\Services\\PluginService))
#25 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#30 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#31 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#32 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Shaqi\\PluginManagement\\Commands\\PluginActivateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 D:\\laragon\\www\\goalconversion\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 {main}
"} 
[2025-07-15 22:41:01] local.ERROR: file_put_contents(D:\laragon\www\goalconversion\storage\framework/cache/data/d7/e6/d7e66de7fabf43550482240a6f4626db8cb8bb93): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): file_put_contents(D:\\laragon\\www\\goalconversion\\storage\\framework/cache/data/d7/e6/d7e66de7fabf43550482240a6f4626db8cb8bb93): Failed to open stream: Permission denied at D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php:204)
[stacktrace]
#0 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(290): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'file_put_conten...', 'D:\\\\laragon\\\\www\\\\...', 204)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'file_put_conten...', 'D:\\\\laragon\\\\www\\\\...', 204)
#2 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(204): file_put_contents('D:\\\\laragon\\\\www\\\\...', '9999999999a:18:...', 2)
#3 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\FileStore.php(83): Illuminate\\Filesystem\\Filesystem->put('D:\\\\laragon\\\\www\\\\...', '9999999999a:18:...', true)
#4 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\FileStore.php(207): Illuminate\\Cache\\FileStore->put('core_installed_...', Array, 0)
#5 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(395): Illuminate\\Cache\\FileStore->forever('core_installed_...', Array)
#6 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(453): Illuminate\\Cache\\Repository->forever('core_installed_...', Array)
#7 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Cache\\CacheManager->__call('forever', Array)
#8 D:\\laragon\\www\\goalconversion\\platform\\packages\\plugin-management\\helpers\\common.php(53): Illuminate\\Support\\Facades\\Facade::__callStatic('forever', Array)
#9 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\plugin-management\\src\\Services\\PluginService.php(79): get_active_plugins()
#10 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\plugin-management\\src\\Commands\\PluginActivateCommand.php(28): Shaqi\\PluginManagement\\Services\\PluginService->activate('portfolio')
#11 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Shaqi\\PluginManagement\\Commands\\PluginActivateCommand->handle(Object(Shaqi\\PluginManagement\\Services\\PluginService))
#12 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#15 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#16 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#17 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#18 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#19 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Shaqi\\PluginManagement\\Commands\\PluginActivateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 D:\\laragon\\www\\goalconversion\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 {main}
"} 
[2025-07-15 22:44:04] local.ERROR: Trait "Shaqi\Base\Supports\TreeCategory\HasTreeCategory" not found {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Trait \"Shaqi\\Base\\Supports\\TreeCategory\\HasTreeCategory\" not found at D:\\laragon\\www\\goalconversion\\platform\\plugins\\portfolio\\src\\Models\\PortfolioCategory.php:13)
[stacktrace]
#0 {main}
"} 
[2025-07-15 22:47:20] local.ERROR: Class "Shaqi\Portfolio\Http\Controllers\ExportPortfolioController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"Shaqi\\Portfolio\\Http\\Controllers\\ExportPortfolioController\" does not exist at D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:235)
[stacktrace]
#0 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(235): ReflectionClass->__construct('Shaqi\\\\Portfolio...')
#1 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(149): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(118): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 339)
#4 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#5 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(795): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map(Object(Closure))
#7 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(103): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#14 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 D:\\laragon\\www\\goalconversion\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-07-15 22:52:35] local.ERROR: Class "Shaqi\Portfolio\Http\Controllers\Settings\PortfolioSettingController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"Shaqi\\Portfolio\\Http\\Controllers\\Settings\\PortfolioSettingController\" does not exist at D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:235)
[stacktrace]
#0 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(235): ReflectionClass->__construct('Shaqi\\\\Portfolio...')
#1 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(149): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(118): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 339)
#4 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#5 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(795): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map(Object(Closure))
#7 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(103): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#14 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 D:\\laragon\\www\\goalconversion\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-07-15 23:16:57] local.ERROR: Undefined array key "type" {"view":{"view":"D:\\laragon\\www\\goalconversion\\platform\\core\\base\\resources\\views\\forms\\partials\\repeater-item.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-885083268 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#2930</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>default</span>\" => <span class=sf-dump-note title=\"Illuminate\\Support\\MessageBag
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>MessageBag</span> {<a class=sf-dump-ref>#2931</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">messages</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">format</span>: \"<span class=sf-dump-str title=\"8 characters\">:message</span>\"
    </samp>}
  </samp>]
</samp>}
</pre><script>Sfdump(\"sf-dump-885083268\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","name":"<pre class=sf-dump id=sf-dump-723262542 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"8 characters\">features</span>\"
</pre><script>Sfdump(\"sf-dump-723262542\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","index":"<pre class=sf-dump id=sf-dump-1467615515 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"7 characters\">__key__</span>\"
</pre><script>Sfdump(\"sf-dump-1467615515\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","key":"<pre class=sf-dump id=sf-dump-958428503 data-indent-pad=\"  \"><span class=sf-dump-num>0</span>
</pre><script>Sfdump(\"sf-dump-958428503\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","field":"<pre class=sf-dump id=sf-dump-1055004049 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>feature</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">feature</span>\"
    \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Feature</span>\"
    \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">feature</span>\"
      \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>
      \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:2</span> [ &#8230;2]
    </samp>]
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-1055004049\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","values":"<pre class=sf-dump id=sf-dump-1674278831 data-indent-pad=\"  \">[]
</pre><script>Sfdump(\"sf-dump-1674278831\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":1,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Undefined array key \"type\" at D:\\laragon\\www\\goalconversion\\platform\\core\\base\\resources\\views\\forms\\partials\\repeater-item.blade.php:1)
[stacktrace]
#0 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#1 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#2 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#3 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#4 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#5 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#6 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Shaqi\\Shortcode\\View\\View->renderContents()
#7 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(506): Illuminate\\View\\View->render()
#8 D:\\laragon\\www\\goalconversion\\platform\\core\\base\\resources\\views\\forms\\partials\\repeater.blade.php(24): Illuminate\\View\\View->__toString()
#9 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#10 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#11 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#12 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#13 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#14 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#15 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Shaqi\\Shortcode\\View\\View->renderContents()
#16 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\form-builder\\src\\Traits\\Componentable.php(38): Illuminate\\View\\View->render()
#17 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\form-builder\\src\\Traits\\Componentable.php(21): Kris\\LaravelFormBuilder\\Supports\\FormBuilder->renderComponent('repeater', Array)
#18 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\form-builder\\src\\Supports\\FormBuilder.php(868): Kris\\LaravelFormBuilder\\Supports\\FormBuilder->componentCall('repeater', Array)
#19 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Kris\\LaravelFormBuilder\\Supports\\FormBuilder->__call('repeater', Array)
#20 D:\\laragon\\www\\goalconversion\\platform\\core\\base\\resources\\views\\forms\\fields\\repeater.blade.php(17): Illuminate\\Support\\Facades\\Facade::__callStatic('repeater', Array)
#21 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#22 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#23 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#24 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#25 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#26 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#27 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Shaqi\\Shortcode\\View\\View->renderContents()
#28 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\form-builder\\src\\Fields\\FormField.php(787): Illuminate\\View\\View->render()
#29 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Traits\\Forms\\CanSpanColumns.php(71): Kris\\LaravelFormBuilder\\Fields\\FormField->render(Array, true, true, true)
#30 D:\\laragon\\www\\goalconversion\\platform\\core\\base\\resources\\views\\forms\\form.blade.php(30): Shaqi\\Base\\Forms\\FormField->render()
#31 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#32 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#33 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#34 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#35 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#36 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#37 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Shaqi\\Shortcode\\View\\View->renderContents()
#38 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\form-builder\\src\\Form.php(326): Illuminate\\View\\View->render()
#39 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\form-builder\\src\\Form.php(307): Kris\\LaravelFormBuilder\\Form->render(Array, Array, true, true, true)
#40 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Forms\\FormAbstract.php(320): Kris\\LaravelFormBuilder\\Form->renderForm(Array, true, true, true)
#41 D:\\laragon\\www\\goalconversion\\platform\\plugins\\portfolio\\src\\Http\\Controllers\\PortfolioController.php(39): Shaqi\\Base\\Forms\\FormAbstract->renderForm()
#42 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Shaqi\\Portfolio\\Http\\Controllers\\PortfolioController->create()
#43 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('create', Array)
#44 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Shaqi\\Portfolio\\Http\\Controllers\\PortfolioController), 'create')
#45 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#46 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#47 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Shaqi\\Base\\Http\\Middleware\\CoreMiddleware->Shaqi\\Base\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\acl\\src\\Http\\Middleware\\Authenticate.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\ACL\\Http\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#71 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#75 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#78 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#79 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#80 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#81 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#82 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#83 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 D:\\laragon\\www\\goalconversion\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#86 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#88 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#90 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#91 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#92 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#93 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#94 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#95 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#96 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#97 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#98 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#99 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#100 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#101 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#102 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#103 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#104 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#105 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#106 D:\\laragon\\www\\goalconversion\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#107 {main}

[previous exception] [object] (ErrorException(code: 0): Undefined array key \"type\" at D:\\laragon\\www\\goalconversion\\storage\\framework\\views\\1b77ecfa423639cdd9a31a303a29f790.php:2)
[stacktrace]
#0 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(290): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined array...', 'D:\\\\laragon\\\\www\\\\...', 2)
#1 D:\\laragon\\www\\goalconversion\\storage\\framework\\views\\1b77ecfa423639cdd9a31a303a29f790.php(2): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined array...', 'D:\\\\laragon\\\\www\\\\...', 2)
#2 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#5 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#6 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#7 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#8 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Shaqi\\Shortcode\\View\\View->renderContents()
#9 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(506): Illuminate\\View\\View->render()
#10 D:\\laragon\\www\\goalconversion\\storage\\framework\\views\\5ee92ad4da0e64adc1858db219b4a4bc.php(24): Illuminate\\View\\View->__toString()
#11 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#12 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#13 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#14 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#15 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#16 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#17 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Shaqi\\Shortcode\\View\\View->renderContents()
#18 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\form-builder\\src\\Traits\\Componentable.php(38): Illuminate\\View\\View->render()
#19 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\form-builder\\src\\Traits\\Componentable.php(21): Kris\\LaravelFormBuilder\\Supports\\FormBuilder->renderComponent('repeater', Array)
#20 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\form-builder\\src\\Supports\\FormBuilder.php(868): Kris\\LaravelFormBuilder\\Supports\\FormBuilder->componentCall('repeater', Array)
#21 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Kris\\LaravelFormBuilder\\Supports\\FormBuilder->__call('repeater', Array)
#22 D:\\laragon\\www\\goalconversion\\storage\\framework\\views\\55efb614251b555176076868fb9f470d.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('repeater', Array)
#23 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#24 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#25 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#26 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#27 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#28 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#29 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Shaqi\\Shortcode\\View\\View->renderContents()
#30 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\form-builder\\src\\Fields\\FormField.php(787): Illuminate\\View\\View->render()
#31 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Traits\\Forms\\CanSpanColumns.php(71): Kris\\LaravelFormBuilder\\Fields\\FormField->render(Array, true, true, true)
#32 D:\\laragon\\www\\goalconversion\\storage\\framework\\views\\6db6f84ad118030bd21c9fd1041886ec.php(48): Shaqi\\Base\\Forms\\FormField->render()
#33 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#34 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#35 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#36 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#37 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#38 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#39 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Shaqi\\Shortcode\\View\\View->renderContents()
#40 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\form-builder\\src\\Form.php(326): Illuminate\\View\\View->render()
#41 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\form-builder\\src\\Form.php(307): Kris\\LaravelFormBuilder\\Form->render(Array, Array, true, true, true)
#42 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Forms\\FormAbstract.php(320): Kris\\LaravelFormBuilder\\Form->renderForm(Array, true, true, true)
#43 D:\\laragon\\www\\goalconversion\\platform\\plugins\\portfolio\\src\\Http\\Controllers\\PortfolioController.php(39): Shaqi\\Base\\Forms\\FormAbstract->renderForm()
#44 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Shaqi\\Portfolio\\Http\\Controllers\\PortfolioController->create()
#45 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('create', Array)
#46 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Shaqi\\Portfolio\\Http\\Controllers\\PortfolioController), 'create')
#47 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#48 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#49 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Shaqi\\Base\\Http\\Middleware\\CoreMiddleware->Shaqi\\Base\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\acl\\src\\Http\\Middleware\\Authenticate.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\ACL\\Http\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#73 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#75 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#80 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#81 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#82 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#83 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#84 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#85 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#86 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 D:\\laragon\\www\\goalconversion\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#88 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#90 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#92 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#93 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#94 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#95 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#96 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#97 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#98 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#99 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#100 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#101 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#102 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#103 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#104 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#105 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#106 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#107 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#108 D:\\laragon\\www\\goalconversion\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#109 {main}
"} 
