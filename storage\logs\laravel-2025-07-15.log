[2025-07-15 22:36:31] local.ERROR: View [partials.portfolios-short-code-admin-config] not found. {"exception":"[object] (InvalidArgumentException(code: 0): View [partials.portfolios-short-code-admin-config] not found. at D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php:139)
[stacktrace]
#0 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php(92): Illuminate\\View\\FileViewFinder->findInPaths('partials.portfo...', Array)
#1 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php(76): Illuminate\\View\\FileViewFinder->findNamespacedView('plugins/portfol...')
#2 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\shortcode\\src\\View\\Factory.php(33): Illuminate\\View\\FileViewFinder->find('plugins/portfol...')
#3 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(1062): Shaqi\\Shortcode\\View\\Factory->make('plugins/portfol...', Array, Array)
#4 D:\\laragon\\www\\goalconversion\\platform\\plugins\\portfolio\\src\\Providers\\HookServiceProvider.php(58): view('plugins/portfol...')
#5 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Shaqi\\Portfolio\\Providers\\HookServiceProvider->boot()
#6 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#7 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#8 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#9 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#10 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1124): Illuminate\\Container\\Container->call(Array)
#11 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(898): Illuminate\\Foundation\\Application->bootProvider(Object(Shaqi\\Portfolio\\Providers\\HookServiceProvider))
#12 D:\\laragon\\www\\goalconversion\\platform\\plugins\\portfolio\\src\\Providers\\PortfolioServiceProvider.php(211): Illuminate\\Foundation\\Application->register(Object(Shaqi\\Portfolio\\Providers\\HookServiceProvider))
#13 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1152): Shaqi\\Portfolio\\Providers\\PortfolioServiceProvider->Shaqi\\Portfolio\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application))
#14 D:\\laragon\\www\\goalconversion\\platform\\plugins\\portfolio\\src\\Providers\\PortfolioServiceProvider.php(205): Illuminate\\Foundation\\Application->booted(Object(Closure))
#15 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Shaqi\\Portfolio\\Providers\\PortfolioServiceProvider->boot()
#16 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#18 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#19 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#20 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1124): Illuminate\\Container\\Container->call(Array)
#21 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(898): Illuminate\\Foundation\\Application->bootProvider(Object(Shaqi\\Portfolio\\Providers\\PortfolioServiceProvider))
#22 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\plugin-management\\src\\Services\\PluginService.php(98): Illuminate\\Foundation\\Application->register(Object(Shaqi\\Portfolio\\Providers\\PortfolioServiceProvider))
#23 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\plugin-management\\src\\Commands\\PluginActivateCommand.php(28): Shaqi\\PluginManagement\\Services\\PluginService->activate('portfolio')
#24 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Shaqi\\PluginManagement\\Commands\\PluginActivateCommand->handle(Object(Shaqi\\PluginManagement\\Services\\PluginService))
#25 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#30 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#31 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#32 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Shaqi\\PluginManagement\\Commands\\PluginActivateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 D:\\laragon\\www\\goalconversion\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 {main}
"} 
[2025-07-15 22:41:01] local.ERROR: file_put_contents(D:\laragon\www\goalconversion\storage\framework/cache/data/d7/e6/d7e66de7fabf43550482240a6f4626db8cb8bb93): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): file_put_contents(D:\\laragon\\www\\goalconversion\\storage\\framework/cache/data/d7/e6/d7e66de7fabf43550482240a6f4626db8cb8bb93): Failed to open stream: Permission denied at D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php:204)
[stacktrace]
#0 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(290): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'file_put_conten...', 'D:\\\\laragon\\\\www\\\\...', 204)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'file_put_conten...', 'D:\\\\laragon\\\\www\\\\...', 204)
#2 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(204): file_put_contents('D:\\\\laragon\\\\www\\\\...', '9999999999a:18:...', 2)
#3 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\FileStore.php(83): Illuminate\\Filesystem\\Filesystem->put('D:\\\\laragon\\\\www\\\\...', '9999999999a:18:...', true)
#4 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\FileStore.php(207): Illuminate\\Cache\\FileStore->put('core_installed_...', Array, 0)
#5 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(395): Illuminate\\Cache\\FileStore->forever('core_installed_...', Array)
#6 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(453): Illuminate\\Cache\\Repository->forever('core_installed_...', Array)
#7 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Cache\\CacheManager->__call('forever', Array)
#8 D:\\laragon\\www\\goalconversion\\platform\\packages\\plugin-management\\helpers\\common.php(53): Illuminate\\Support\\Facades\\Facade::__callStatic('forever', Array)
#9 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\plugin-management\\src\\Services\\PluginService.php(79): get_active_plugins()
#10 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\plugin-management\\src\\Commands\\PluginActivateCommand.php(28): Shaqi\\PluginManagement\\Services\\PluginService->activate('portfolio')
#11 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Shaqi\\PluginManagement\\Commands\\PluginActivateCommand->handle(Object(Shaqi\\PluginManagement\\Services\\PluginService))
#12 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#15 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#16 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#17 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#18 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#19 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Shaqi\\PluginManagement\\Commands\\PluginActivateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 D:\\laragon\\www\\goalconversion\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 {main}
"} 
[2025-07-15 22:44:04] local.ERROR: Trait "Shaqi\Base\Supports\TreeCategory\HasTreeCategory" not found {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Trait \"Shaqi\\Base\\Supports\\TreeCategory\\HasTreeCategory\" not found at D:\\laragon\\www\\goalconversion\\platform\\plugins\\portfolio\\src\\Models\\PortfolioCategory.php:13)
[stacktrace]
#0 {main}
"} 
[2025-07-15 22:47:20] local.ERROR: Class "Shaqi\Portfolio\Http\Controllers\ExportPortfolioController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"Shaqi\\Portfolio\\Http\\Controllers\\ExportPortfolioController\" does not exist at D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:235)
[stacktrace]
#0 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(235): ReflectionClass->__construct('Shaqi\\\\Portfolio...')
#1 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(149): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(118): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 339)
#4 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#5 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(795): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map(Object(Closure))
#7 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(103): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#14 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 D:\\laragon\\www\\goalconversion\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-07-15 22:52:35] local.ERROR: Class "Shaqi\Portfolio\Http\Controllers\Settings\PortfolioSettingController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"Shaqi\\Portfolio\\Http\\Controllers\\Settings\\PortfolioSettingController\" does not exist at D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:235)
[stacktrace]
#0 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(235): ReflectionClass->__construct('Shaqi\\\\Portfolio...')
#1 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(149): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(118): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 339)
#4 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#5 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(795): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map(Object(Closure))
#7 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(103): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#14 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 D:\\laragon\\www\\goalconversion\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
